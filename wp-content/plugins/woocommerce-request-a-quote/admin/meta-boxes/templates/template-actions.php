<?php
/**
 * Template Actions Meta Box
 *
 * @package addify-request-a-quote
 * @version 1.6.0
 */

defined( 'ABSPATH' ) || exit;

global $post;
$template_id = $post->ID;
?>

<div class="afrfq-template-actions">
	<div class="template-action-section">
		<h4><?php esc_html_e( 'Template Actions', 'addify_rfq' ); ?></h4>
		
		<div class="action-buttons">
			<button type="button" class="button button-secondary template-preview-btn" data-template-id="<?php echo esc_attr( $template_id ); ?>">
				<span class="dashicons dashicons-visibility"></span>
				<?php esc_html_e( 'Preview Template', 'addify_rfq' ); ?>
			</button>
			
			<button type="button" class="button button-secondary template-export-btn" data-template-id="<?php echo esc_attr( $template_id ); ?>">
				<span class="dashicons dashicons-download"></span>
				<?php esc_html_e( 'Export Template', 'addify_rfq' ); ?>
			</button>
			
			<button type="button" class="button button-primary template-apply-btn" data-template-id="<?php echo esc_attr( $template_id ); ?>">
				<span class="dashicons dashicons-admin-tools"></span>
				<?php esc_html_e( 'Apply to New Form', 'addify_rfq' ); ?>
			</button>
		</div>
	</div>

	<div class="template-action-section">
		<h4><?php esc_html_e( 'Import Template', 'addify_rfq' ); ?></h4>
		<p class="description"><?php esc_html_e( 'Import a template from a JSON file.', 'addify_rfq' ); ?></p>
		
		<div class="import-section">
			<input type="file" id="template-import-file" accept=".json" style="display: none;" />
			<button type="button" class="button button-secondary" onclick="document.getElementById('template-import-file').click();">
				<span class="dashicons dashicons-upload"></span>
				<?php esc_html_e( 'Choose File', 'addify_rfq' ); ?>
			</button>
			<span id="selected-file-name" style="margin-left: 10px; font-style: italic;"></span>
			
			<div style="margin-top: 10px;">
				<button type="button" class="button button-primary template-import-btn" disabled>
					<span class="dashicons dashicons-upload"></span>
					<?php esc_html_e( 'Import Template', 'addify_rfq' ); ?>
				</button>
			</div>
		</div>
	</div>

	<div class="template-action-section">
		<h4><?php esc_html_e( 'Template Statistics', 'addify_rfq' ); ?></h4>
		<?php
		$template_fields = get_post_meta( $template_id, '_afrfq_template_fields', true );
		$field_count = is_array( $template_fields ) ? count( $template_fields ) : 0;
		$created_date = get_the_date( 'Y-m-d H:i:s', $template_id );
		$modified_date = get_the_modified_date( 'Y-m-d H:i:s', $template_id );
		?>
		
		<div class="template-stats">
			<div class="stat-item">
				<strong><?php esc_html_e( 'Fields:', 'addify_rfq' ); ?></strong>
				<span><?php echo esc_html( $field_count ); ?></span>
			</div>
			<div class="stat-item">
				<strong><?php esc_html_e( 'Created:', 'addify_rfq' ); ?></strong>
				<span><?php echo esc_html( $created_date ); ?></span>
			</div>
			<div class="stat-item">
				<strong><?php esc_html_e( 'Modified:', 'addify_rfq' ); ?></strong>
				<span><?php echo esc_html( $modified_date ); ?></span>
			</div>
		</div>
	</div>
</div>

<!-- Template Preview Modal -->
<div id="template-preview-modal" class="afrfq-modal" style="display: none;">
	<div class="afrfq-modal-content">
		<div class="afrfq-modal-header">
			<h3><?php esc_html_e( 'Template Preview', 'addify_rfq' ); ?></h3>
			<button type="button" class="afrfq-modal-close">&times;</button>
		</div>
		<div class="afrfq-modal-body">
			<div id="template-preview-content">
				<!-- Preview content will be loaded here -->
			</div>
		</div>
		<div class="afrfq-modal-footer">
			<button type="button" class="button button-secondary afrfq-modal-close">
				<?php esc_html_e( 'Close', 'addify_rfq' ); ?>
			</button>
		</div>
	</div>
</div>

<style>
.afrfq-template-actions {
	padding: 15px;
}

.template-action-section {
	margin-bottom: 25px;
	padding-bottom: 20px;
	border-bottom: 1px solid #e1e1e1;
}

.template-action-section:last-child {
	border-bottom: none;
	margin-bottom: 0;
}

.template-action-section h4 {
	margin: 0 0 10px 0;
	font-size: 14px;
	font-weight: 600;
}

.action-buttons {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.action-buttons .button {
	display: flex;
	align-items: center;
	gap: 5px;
	justify-content: flex-start;
	text-align: left;
}

.import-section {
	margin-top: 10px;
}

.template-stats {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.stat-item {
	display: flex;
	justify-content: space-between;
	padding: 5px 0;
	border-bottom: 1px solid #f1f1f1;
}

.stat-item:last-child {
	border-bottom: none;
}

/* Modal Styles */
.afrfq-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.7);
	z-index: 100000;
	display: flex;
	align-items: center;
	justify-content: center;
}

.afrfq-modal-content {
	background: white;
	border-radius: 8px;
	max-width: 800px;
	max-height: 90vh;
	width: 90%;
	overflow: hidden;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.afrfq-modal-header {
	padding: 20px;
	border-bottom: 1px solid #e1e1e1;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.afrfq-modal-header h3 {
	margin: 0;
	font-size: 18px;
}

.afrfq-modal-close {
	background: none;
	border: none;
	font-size: 24px;
	cursor: pointer;
	padding: 0;
	width: 30px;
	height: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.afrfq-modal-body {
	padding: 20px;
	max-height: 60vh;
	overflow-y: auto;
}

.afrfq-modal-footer {
	padding: 15px 20px;
	border-top: 1px solid #e1e1e1;
	text-align: right;
}

/* Template Preview Styles */
.afrfq-template-preview {
	max-width: 600px;
}

.afrfq-preview-field {
	margin-bottom: 20px;
	padding: 15px;
	border: 1px solid #e1e1e1;
	border-radius: 6px;
	background: #fafafa;
}

.afrfq-preview-label {
	display: block;
	font-weight: 600;
	margin-bottom: 8px;
	color: #333;
}

.afrfq-preview-label .required {
	color: #e74c3c;
}

.afrfq-preview-input,
.afrfq-preview-option {
	width: 100%;
	padding: 8px 12px;
	border: 1px solid #ddd;
	border-radius: 4px;
	font-size: 14px;
}

.afrfq-preview-option {
	display: block;
	margin-bottom: 8px;
	width: auto;
}

.afrfq-preview-option input {
	margin-right: 8px;
}

.afrfq-preview-description {
	display: block;
	margin-top: 5px;
	color: #666;
	font-style: italic;
	font-size: 12px;
}

/* Loading States */
.button.loading {
	opacity: 0.7;
	pointer-events: none;
}

.button.loading::before {
	content: '';
	display: inline-block;
	width: 16px;
	height: 16px;
	border: 2px solid #ccc;
	border-top: 2px solid #333;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-right: 8px;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}
</style>

<script>
jQuery(document).ready(function($) {
	// File selection handler
	$('#template-import-file').on('change', function() {
		var fileName = this.files[0] ? this.files[0].name : '';
		$('#selected-file-name').text(fileName);
		$('.template-import-btn').prop('disabled', !fileName);
	});

	// Modal close handlers
	$('.afrfq-modal-close').on('click', function() {
		$('#template-preview-modal').hide();
	});

	$(document).on('click', function(e) {
		if ($(e.target).hasClass('afrfq-modal')) {
			$('#template-preview-modal').hide();
		}
	});
});
</script>
