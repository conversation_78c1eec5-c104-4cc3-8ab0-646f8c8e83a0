<?php
/**
 * Template Fields Meta Box
 *
 * @package addify-request-a-quote
 * @version 1.6.0
 */

defined( 'ABSPATH' ) || exit;

$quote_fields_obj = new AF_R_F_Q_Quote_Fields();
$field_types = $quote_fields_obj->get_field_types();
?>

<div class="afrfq-template-fields">
	<div class="template-fields-header">
		<h4><?php esc_html_e( 'Template Fields Configuration', 'addify_rfq' ); ?></h4>
		<p class="description"><?php esc_html_e( 'Configure the fields that will be included in this template. You can drag and drop to reorder fields.', 'addify_rfq' ); ?></p>
		<button type="button" class="button button-secondary add-template-field">
			<span class="dashicons dashicons-plus-alt2"></span>
			<?php esc_html_e( 'Add Field', 'addify_rfq' ); ?>
		</button>
	</div>

	<div class="template-fields-list" id="template-fields-sortable">
		<?php if ( ! empty( $template_fields ) ) : ?>
			<?php foreach ( $template_fields as $index => $field ) : ?>
				<div class="template-field-item" data-index="<?php echo esc_attr( $index ); ?>">
					<div class="field-header">
						<span class="field-handle dashicons dashicons-menu"></span>
						<span class="field-title"><?php echo esc_html( $field['label'] ?? 'Field ' . ( $index + 1 ) ); ?></span>
						<span class="field-type">(<?php echo esc_html( $field['type'] ?? 'text' ); ?>)</span>
						<div class="field-actions">
							<button type="button" class="toggle-field-config" title="<?php esc_attr_e( 'Toggle Configuration', 'addify_rfq' ); ?>">
								<span class="dashicons dashicons-arrow-down-alt2"></span>
							</button>
							<button type="button" class="remove-field" title="<?php esc_attr_e( 'Remove Field', 'addify_rfq' ); ?>">
								<span class="dashicons dashicons-trash"></span>
							</button>
						</div>
					</div>
					
					<div class="field-config" style="display: none;">
						<table class="field-config-table">
							<tr>
								<td>
									<label><?php esc_html_e( 'Field Name', 'addify_rfq' ); ?></label>
									<input type="text" name="_afrfq_template_fields[<?php echo esc_attr( $index ); ?>][name]" 
										   value="<?php echo esc_attr( $field['name'] ?? '' ); ?>" 
										   placeholder="field_name" class="field-name-input" />
								</td>
								<td>
									<label><?php esc_html_e( 'Field Type', 'addify_rfq' ); ?></label>
									<select name="_afrfq_template_fields[<?php echo esc_attr( $index ); ?>][type]" class="field-type-select">
										<?php foreach ( $field_types as $type => $config ) : ?>
											<option value="<?php echo esc_attr( $type ); ?>" <?php selected( $field['type'] ?? '', $type ); ?>>
												<?php echo esc_html( $config['label'] ); ?>
											</option>
										<?php endforeach; ?>
									</select>
								</td>
								<td>
									<label><?php esc_html_e( 'Field Label', 'addify_rfq' ); ?></label>
									<input type="text" name="_afrfq_template_fields[<?php echo esc_attr( $index ); ?>][label]" 
										   value="<?php echo esc_attr( $field['label'] ?? '' ); ?>" 
										   placeholder="Field Label" class="field-label-input" />
								</td>
							</tr>
							<tr>
								<td>
									<label><?php esc_html_e( 'Placeholder', 'addify_rfq' ); ?></label>
									<input type="text" name="_afrfq_template_fields[<?php echo esc_attr( $index ); ?>][placeholder]" 
										   value="<?php echo esc_attr( $field['placeholder'] ?? '' ); ?>" 
										   placeholder="Enter placeholder text" />
								</td>
								<td>
									<label><?php esc_html_e( 'Required', 'addify_rfq' ); ?></label>
									<select name="_afrfq_template_fields[<?php echo esc_attr( $index ); ?>][required]">
										<option value="no" <?php selected( $field['required'] ?? 'no', 'no' ); ?>><?php esc_html_e( 'No', 'addify_rfq' ); ?></option>
										<option value="yes" <?php selected( $field['required'] ?? 'no', 'yes' ); ?>><?php esc_html_e( 'Yes', 'addify_rfq' ); ?></option>
									</select>
								</td>
								<td>
									<label><?php esc_html_e( 'Width', 'addify_rfq' ); ?></label>
									<select name="_afrfq_template_fields[<?php echo esc_attr( $index ); ?>][width]">
										<option value="full_width" <?php selected( $field['width'] ?? 'full_width', 'full_width' ); ?>><?php esc_html_e( 'Full Width', 'addify_rfq' ); ?></option>
										<option value="half_width" <?php selected( $field['width'] ?? 'full_width', 'half_width' ); ?>><?php esc_html_e( 'Half Width', 'addify_rfq' ); ?></option>
										<option value="third_width" <?php selected( $field['width'] ?? 'full_width', 'third_width' ); ?>><?php esc_html_e( 'Third Width', 'addify_rfq' ); ?></option>
										<option value="quarter_width" <?php selected( $field['width'] ?? 'full_width', 'quarter_width' ); ?>><?php esc_html_e( 'Quarter Width', 'addify_rfq' ); ?></option>
									</select>
								</td>
							</tr>
							<tr>
								<td colspan="3">
									<label><?php esc_html_e( 'Description', 'addify_rfq' ); ?></label>
									<textarea name="_afrfq_template_fields[<?php echo esc_attr( $index ); ?>][description]" 
											  rows="2" placeholder="Field description or help text"><?php echo esc_textarea( $field['description'] ?? '' ); ?></textarea>
								</td>
							</tr>
							<tr class="field-options-row" style="<?php echo in_array( $field['type'] ?? '', array( 'select', 'radio', 'checkbox' ) ) ? '' : 'display: none;'; ?>">
								<td colspan="3">
									<label><?php esc_html_e( 'Options (one per line)', 'addify_rfq' ); ?></label>
									<textarea name="_afrfq_template_fields[<?php echo esc_attr( $index ); ?>][options]" 
											  rows="3" placeholder="Option 1&#10;Option 2&#10;Option 3"><?php 
										if ( ! empty( $field['options'] ) ) {
											echo esc_textarea( is_array( $field['options'] ) ? implode( "\n", $field['options'] ) : $field['options'] );
										}
									?></textarea>
								</td>
							</tr>
							<tr class="field-file-types-row" style="<?php echo ( $field['type'] ?? '' ) === 'file' ? '' : 'display: none;'; ?>">
								<td>
									<label><?php esc_html_e( 'Allowed File Types', 'addify_rfq' ); ?></label>
									<input type="text" name="_afrfq_template_fields[<?php echo esc_attr( $index ); ?>][file_types]" 
										   value="<?php echo esc_attr( $field['file_types'] ?? 'pdf,doc,docx,jpg,png' ); ?>" 
										   placeholder="pdf,doc,jpg,png" />
								</td>
								<td>
									<label><?php esc_html_e( 'Max File Size (bytes)', 'addify_rfq' ); ?></label>
									<input type="number" name="_afrfq_template_fields[<?php echo esc_attr( $index ); ?>][file_size]" 
										   value="<?php echo esc_attr( $field['file_size'] ?? '10000000' ); ?>" 
										   placeholder="10000000" />
								</td>
								<td></td>
							</tr>
							<tr class="field-validation-row" style="<?php echo in_array( $field['type'] ?? '', array( 'text', 'textarea', 'number' ) ) ? '' : 'display: none;'; ?>">
								<td>
									<label><?php esc_html_e( 'Min Length/Value', 'addify_rfq' ); ?></label>
									<input type="number" name="_afrfq_template_fields[<?php echo esc_attr( $index ); ?>][min_length]" 
										   value="<?php echo esc_attr( $field['min_length'] ?? '' ); ?>" 
										   placeholder="0" />
								</td>
								<td>
									<label><?php esc_html_e( 'Max Length/Value', 'addify_rfq' ); ?></label>
									<input type="number" name="_afrfq_template_fields[<?php echo esc_attr( $index ); ?>][max_length]" 
										   value="<?php echo esc_attr( $field['max_length'] ?? '' ); ?>" 
										   placeholder="255" />
								</td>
								<td>
									<label><?php esc_html_e( 'Pattern (Regex)', 'addify_rfq' ); ?></label>
									<input type="text" name="_afrfq_template_fields[<?php echo esc_attr( $index ); ?>][pattern]" 
										   value="<?php echo esc_attr( $field['pattern'] ?? '' ); ?>" 
										   placeholder="^[A-Za-z0-9]+$" />
								</td>
							</tr>
							<tr>
								<td>
									<label><?php esc_html_e( 'CSS Class', 'addify_rfq' ); ?></label>
									<input type="text" name="_afrfq_template_fields[<?php echo esc_attr( $index ); ?>][css_class]" 
										   value="<?php echo esc_attr( $field['css_class'] ?? '' ); ?>" 
										   placeholder="custom-class" />
								</td>
								<td>
									<label><?php esc_html_e( 'Order', 'addify_rfq' ); ?></label>
									<input type="number" name="_afrfq_template_fields[<?php echo esc_attr( $index ); ?>][order]" 
										   value="<?php echo esc_attr( $field['order'] ?? ( $index + 1 ) ); ?>" 
										   min="1" class="field-order-input" />
								</td>
								<td></td>
							</tr>
						</table>
					</div>
				</div>
			<?php endforeach; ?>
		<?php endif; ?>
	</div>

	<div class="template-fields-footer">
		<button type="button" class="button button-secondary add-template-field">
			<span class="dashicons dashicons-plus-alt2"></span>
			<?php esc_html_e( 'Add Another Field', 'addify_rfq' ); ?>
		</button>
	</div>
</div>

<!-- Field Template for JavaScript -->
<script type="text/template" id="template-field-template">
	<div class="template-field-item" data-index="{{INDEX}}">
		<div class="field-header">
			<span class="field-handle dashicons dashicons-menu"></span>
			<span class="field-title"><?php esc_html_e( 'New Field', 'addify_rfq' ); ?></span>
			<span class="field-type">(text)</span>
			<div class="field-actions">
				<button type="button" class="toggle-field-config" title="<?php esc_attr_e( 'Toggle Configuration', 'addify_rfq' ); ?>">
					<span class="dashicons dashicons-arrow-down-alt2"></span>
				</button>
				<button type="button" class="remove-field" title="<?php esc_attr_e( 'Remove Field', 'addify_rfq' ); ?>">
					<span class="dashicons dashicons-trash"></span>
				</button>
			</div>
		</div>
		
		<div class="field-config">
			<table class="field-config-table">
				<tr>
					<td>
						<label><?php esc_html_e( 'Field Name', 'addify_rfq' ); ?></label>
						<input type="text" name="_afrfq_template_fields[{{INDEX}}][name]" 
							   value="" placeholder="field_name" class="field-name-input" />
					</td>
					<td>
						<label><?php esc_html_e( 'Field Type', 'addify_rfq' ); ?></label>
						<select name="_afrfq_template_fields[{{INDEX}}][type]" class="field-type-select">
							<?php foreach ( $field_types as $type => $config ) : ?>
								<option value="<?php echo esc_attr( $type ); ?>">
									<?php echo esc_html( $config['label'] ); ?>
								</option>
							<?php endforeach; ?>
						</select>
					</td>
					<td>
						<label><?php esc_html_e( 'Field Label', 'addify_rfq' ); ?></label>
						<input type="text" name="_afrfq_template_fields[{{INDEX}}][label]" 
							   value="" placeholder="Field Label" class="field-label-input" />
					</td>
				</tr>
				<tr>
					<td>
						<label><?php esc_html_e( 'Placeholder', 'addify_rfq' ); ?></label>
						<input type="text" name="_afrfq_template_fields[{{INDEX}}][placeholder]" 
							   value="" placeholder="Enter placeholder text" />
					</td>
					<td>
						<label><?php esc_html_e( 'Required', 'addify_rfq' ); ?></label>
						<select name="_afrfq_template_fields[{{INDEX}}][required]">
							<option value="no"><?php esc_html_e( 'No', 'addify_rfq' ); ?></option>
							<option value="yes"><?php esc_html_e( 'Yes', 'addify_rfq' ); ?></option>
						</select>
					</td>
					<td>
						<label><?php esc_html_e( 'Width', 'addify_rfq' ); ?></label>
						<select name="_afrfq_template_fields[{{INDEX}}][width]">
							<option value="full_width"><?php esc_html_e( 'Full Width', 'addify_rfq' ); ?></option>
							<option value="half_width"><?php esc_html_e( 'Half Width', 'addify_rfq' ); ?></option>
							<option value="third_width"><?php esc_html_e( 'Third Width', 'addify_rfq' ); ?></option>
							<option value="quarter_width"><?php esc_html_e( 'Quarter Width', 'addify_rfq' ); ?></option>
						</select>
					</td>
				</tr>
				<tr>
					<td colspan="3">
						<label><?php esc_html_e( 'Description', 'addify_rfq' ); ?></label>
						<textarea name="_afrfq_template_fields[{{INDEX}}][description]" 
								  rows="2" placeholder="Field description or help text"></textarea>
					</td>
				</tr>
				<tr class="field-options-row" style="display: none;">
					<td colspan="3">
						<label><?php esc_html_e( 'Options (one per line)', 'addify_rfq' ); ?></label>
						<textarea name="_afrfq_template_fields[{{INDEX}}][options]" 
								  rows="3" placeholder="Option 1&#10;Option 2&#10;Option 3"></textarea>
					</td>
				</tr>
				<tr class="field-file-types-row" style="display: none;">
					<td>
						<label><?php esc_html_e( 'Allowed File Types', 'addify_rfq' ); ?></label>
						<input type="text" name="_afrfq_template_fields[{{INDEX}}][file_types]" 
							   value="pdf,doc,docx,jpg,png" placeholder="pdf,doc,jpg,png" />
					</td>
					<td>
						<label><?php esc_html_e( 'Max File Size (bytes)', 'addify_rfq' ); ?></label>
						<input type="number" name="_afrfq_template_fields[{{INDEX}}][file_size]" 
							   value="10000000" placeholder="10000000" />
					</td>
					<td></td>
				</tr>
				<tr class="field-validation-row">
					<td>
						<label><?php esc_html_e( 'Min Length/Value', 'addify_rfq' ); ?></label>
						<input type="number" name="_afrfq_template_fields[{{INDEX}}][min_length]" 
							   value="" placeholder="0" />
					</td>
					<td>
						<label><?php esc_html_e( 'Max Length/Value', 'addify_rfq' ); ?></label>
						<input type="number" name="_afrfq_template_fields[{{INDEX}}][max_length]" 
							   value="" placeholder="255" />
					</td>
					<td>
						<label><?php esc_html_e( 'Pattern (Regex)', 'addify_rfq' ); ?></label>
						<input type="text" name="_afrfq_template_fields[{{INDEX}}][pattern]" 
							   value="" placeholder="^[A-Za-z0-9]+$" />
					</td>
				</tr>
				<tr>
					<td>
						<label><?php esc_html_e( 'CSS Class', 'addify_rfq' ); ?></label>
						<input type="text" name="_afrfq_template_fields[{{INDEX}}][css_class]" 
							   value="" placeholder="custom-class" />
					</td>
					<td>
						<label><?php esc_html_e( 'Order', 'addify_rfq' ); ?></label>
						<input type="number" name="_afrfq_template_fields[{{INDEX}}][order]" 
							   value="{{ORDER}}" min="1" class="field-order-input" />
					</td>
					<td></td>
				</tr>
			</table>
		</div>
	</div>
</script>
