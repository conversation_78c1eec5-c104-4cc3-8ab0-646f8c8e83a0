<?php
/**
 * Template Configuration Meta Box
 *
 * @package addify-request-a-quote
 * @version 1.6.0
 */

defined( 'ABSPATH' ) || exit;

$template_obj = new AF_R_F_Q_Form_Templates();
$categories = $template_obj->get_template_categories();
$industries = $template_obj->get_template_industries();
?>

<div class="afrfq-template-config">
	<table class="form-table">
		<tr>
			<th scope="row">
				<label for="afrfq_template_category"><?php esc_html_e( 'Template Category', 'addify_rfq' ); ?></label>
			</th>
			<td>
				<select name="_afrfq_template_category" id="afrfq_template_category" class="regular-text">
					<?php foreach ( $categories as $value => $label ) : ?>
						<option value="<?php echo esc_attr( $value ); ?>" <?php selected( $template_category, $value ); ?>>
							<?php echo esc_html( $label ); ?>
						</option>
					<?php endforeach; ?>
				</select>
				<p class="description"><?php esc_html_e( 'Select the category that best describes this template.', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		
		<tr>
			<th scope="row">
				<label for="afrfq_template_industry"><?php esc_html_e( 'Industry', 'addify_rfq' ); ?></label>
			</th>
			<td>
				<select name="_afrfq_template_industry" id="afrfq_template_industry" class="regular-text">
					<?php foreach ( $industries as $value => $label ) : ?>
						<option value="<?php echo esc_attr( $value ); ?>" <?php selected( $template_industry, $value ); ?>>
							<?php echo esc_html( $label ); ?>
						</option>
					<?php endforeach; ?>
				</select>
				<p class="description"><?php esc_html_e( 'Select the industry this template is designed for.', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		
		<tr>
			<th scope="row">
				<label for="afrfq_template_description"><?php esc_html_e( 'Description', 'addify_rfq' ); ?></label>
			</th>
			<td>
				<textarea name="_afrfq_template_description" id="afrfq_template_description" rows="3" cols="50" class="large-text"><?php echo esc_textarea( $template_description ); ?></textarea>
				<p class="description"><?php esc_html_e( 'Provide a detailed description of this template and its intended use.', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		
		<tr>
			<th scope="row">
				<label for="afrfq_template_tags"><?php esc_html_e( 'Tags', 'addify_rfq' ); ?></label>
			</th>
			<td>
				<input type="text" name="_afrfq_template_tags" id="afrfq_template_tags" value="<?php echo esc_attr( $template_tags ); ?>" class="regular-text" />
				<p class="description"><?php esc_html_e( 'Enter comma-separated tags to help categorize and search for this template.', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		
		<tr>
			<th scope="row">
				<label for="afrfq_template_version"><?php esc_html_e( 'Version', 'addify_rfq' ); ?></label>
			</th>
			<td>
				<input type="text" name="_afrfq_template_version" id="afrfq_template_version" value="<?php echo esc_attr( $template_version ); ?>" class="small-text" />
				<p class="description"><?php esc_html_e( 'Template version number (e.g., 1.0.0).', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		
		<tr>
			<th scope="row">
				<label for="afrfq_template_author"><?php esc_html_e( 'Author', 'addify_rfq' ); ?></label>
			</th>
			<td>
				<input type="text" name="_afrfq_template_author" id="afrfq_template_author" value="<?php echo esc_attr( $template_author ); ?>" class="regular-text" />
				<p class="description"><?php esc_html_e( 'Template author or creator name.', 'addify_rfq' ); ?></p>
			</td>
		</tr>
	</table>
</div>

<style>
.afrfq-template-config .form-table th {
	width: 200px;
	padding: 15px 10px 15px 0;
}

.afrfq-template-config .form-table td {
	padding: 15px 10px;
}

.afrfq-template-config .description {
	margin-top: 5px;
	font-style: italic;
	color: #666;
}
</style>
