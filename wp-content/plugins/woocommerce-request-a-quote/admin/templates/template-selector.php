<?php
/**
 * Template Selector Interface
 *
 * @package addify-request-a-quote
 * @version 1.6.0
 */

defined( 'ABSPATH' ) || exit;

$templates_obj = new AF_R_F_Q_Form_Templates();
$templates = $templates_obj->get_templates();
$categories = $templates_obj->get_template_categories();
$industries = $templates_obj->get_template_industries();
?>

<div class="wrap">
	<h1 class="wp-heading-inline"><?php esc_html_e( 'Form Templates', 'addify_rfq' ); ?></h1>
	<a href="<?php echo esc_url( admin_url( 'post-new.php?post_type=afrfq_form_template' ) ); ?>" class="page-title-action">
		<?php esc_html_e( 'Add New Template', 'addify_rfq' ); ?>
	</a>
	<hr class="wp-header-end">

	<div class="afrfq-template-selector">
		<!-- Template Filters -->
		<div class="template-filters">
			<div class="filter-group">
				<label for="template-category-filter"><?php esc_html_e( 'Category:', 'addify_rfq' ); ?></label>
				<select id="template-category-filter">
					<option value=""><?php esc_html_e( 'All Categories', 'addify_rfq' ); ?></option>
					<?php foreach ( $categories as $value => $label ) : ?>
						<option value="<?php echo esc_attr( $value ); ?>"><?php echo esc_html( $label ); ?></option>
					<?php endforeach; ?>
				</select>
			</div>

			<div class="filter-group">
				<label for="template-industry-filter"><?php esc_html_e( 'Industry:', 'addify_rfq' ); ?></label>
				<select id="template-industry-filter">
					<option value=""><?php esc_html_e( 'All Industries', 'addify_rfq' ); ?></option>
					<?php foreach ( $industries as $value => $label ) : ?>
						<option value="<?php echo esc_attr( $value ); ?>"><?php echo esc_html( $label ); ?></option>
					<?php endforeach; ?>
				</select>
			</div>

			<div class="filter-group">
				<input type="text" id="template-search" placeholder="<?php esc_attr_e( 'Search templates...', 'addify_rfq' ); ?>" />
			</div>
		</div>

		<!-- Template Grid -->
		<div class="template-grid" id="template-grid">
			<?php if ( ! empty( $templates ) ) : ?>
				<?php foreach ( $templates as $template ) : ?>
					<div class="template-card" 
						 data-template-id="<?php echo esc_attr( $template['id'] ); ?>"
						 data-category="<?php echo esc_attr( $template['category'] ); ?>"
						 data-industry="<?php echo esc_attr( $template['industry'] ); ?>"
						 data-tags="<?php echo esc_attr( $template['tags'] ); ?>">
						
						<?php if ( ! empty( $template['thumbnail'] ) ) : ?>
							<div class="template-thumbnail">
								<img src="<?php echo esc_url( $template['thumbnail'] ); ?>" alt="<?php echo esc_attr( $template['title'] ); ?>" />
							</div>
						<?php else : ?>
							<div class="template-thumbnail template-thumbnail-placeholder">
								<span class="dashicons dashicons-forms"></span>
							</div>
						<?php endif; ?>

						<div class="template-content">
							<h3 class="template-title"><?php echo esc_html( $template['title'] ); ?></h3>
							
							<div class="template-meta">
								<span class="template-category"><?php echo esc_html( $categories[ $template['category'] ] ?? $template['category'] ); ?></span>
								<span class="template-industry"><?php echo esc_html( $industries[ $template['industry'] ] ?? $template['industry'] ); ?></span>
								<span class="template-fields-count"><?php echo esc_html( count( $template['fields'] ?? array() ) ); ?> <?php esc_html_e( 'fields', 'addify_rfq' ); ?></span>
							</div>

							<?php if ( ! empty( $template['description'] ) ) : ?>
								<p class="template-description"><?php echo esc_html( wp_trim_words( $template['description'], 20 ) ); ?></p>
							<?php endif; ?>

							<?php if ( ! empty( $template['tags'] ) ) : ?>
								<div class="template-tags">
									<?php 
									$tags = explode( ',', $template['tags'] );
									foreach ( $tags as $tag ) : 
										$tag = trim( $tag );
										if ( ! empty( $tag ) ) :
									?>
										<span class="template-tag"><?php echo esc_html( $tag ); ?></span>
									<?php 
										endif;
									endforeach; 
									?>
								</div>
							<?php endif; ?>
						</div>

						<div class="template-actions">
							<button type="button" class="button button-secondary template-preview-btn" data-template-id="<?php echo esc_attr( $template['id'] ); ?>">
								<span class="dashicons dashicons-visibility"></span>
								<?php esc_html_e( 'Preview', 'addify_rfq' ); ?>
							</button>
							
							<button type="button" class="button button-primary template-apply-btn" data-template-id="<?php echo esc_attr( $template['id'] ); ?>">
								<span class="dashicons dashicons-admin-tools"></span>
								<?php esc_html_e( 'Apply Template', 'addify_rfq' ); ?>
							</button>
							
							<div class="template-more-actions">
								<button type="button" class="button button-link template-more-btn">
									<span class="dashicons dashicons-ellipsis"></span>
								</button>
								<div class="template-more-menu" style="display: none;">
									<a href="<?php echo esc_url( admin_url( 'post.php?post=' . $template['id'] . '&action=edit' ) ); ?>" class="template-edit-link">
										<span class="dashicons dashicons-edit"></span>
										<?php esc_html_e( 'Edit', 'addify_rfq' ); ?>
									</a>
									<button type="button" class="template-export-btn" data-template-id="<?php echo esc_attr( $template['id'] ); ?>">
										<span class="dashicons dashicons-download"></span>
										<?php esc_html_e( 'Export', 'addify_rfq' ); ?>
									</button>
									<button type="button" class="template-duplicate-btn" data-template-id="<?php echo esc_attr( $template['id'] ); ?>">
										<span class="dashicons dashicons-admin-page"></span>
										<?php esc_html_e( 'Duplicate', 'addify_rfq' ); ?>
									</button>
								</div>
							</div>
						</div>
					</div>
				<?php endforeach; ?>
			<?php else : ?>
				<div class="template-empty-state">
					<div class="empty-state-icon">
						<span class="dashicons dashicons-forms"></span>
					</div>
					<h3><?php esc_html_e( 'No Templates Found', 'addify_rfq' ); ?></h3>
					<p><?php esc_html_e( 'Get started by creating your first form template or importing an existing one.', 'addify_rfq' ); ?></p>
					<div class="empty-state-actions">
						<a href="<?php echo esc_url( admin_url( 'post-new.php?post_type=afrfq_form_template' ) ); ?>" class="button button-primary">
							<?php esc_html_e( 'Create Template', 'addify_rfq' ); ?>
						</a>
						<button type="button" class="button button-secondary" id="import-template-btn">
							<?php esc_html_e( 'Import Template', 'addify_rfq' ); ?>
						</button>
					</div>
				</div>
			<?php endif; ?>
		</div>
	</div>

	<!-- Import Template Modal -->
	<div id="import-template-modal" class="afrfq-modal" style="display: none;">
		<div class="afrfq-modal-content">
			<div class="afrfq-modal-header">
				<h3><?php esc_html_e( 'Import Template', 'addify_rfq' ); ?></h3>
				<button type="button" class="afrfq-modal-close">&times;</button>
			</div>
			<div class="afrfq-modal-body">
				<p><?php esc_html_e( 'Select a JSON template file to import:', 'addify_rfq' ); ?></p>
				<input type="file" id="import-template-file" accept=".json" />
				<div id="import-progress" style="display: none;">
					<div class="import-progress-bar">
						<div class="import-progress-fill"></div>
					</div>
					<p class="import-status"><?php esc_html_e( 'Importing template...', 'addify_rfq' ); ?></p>
				</div>
			</div>
			<div class="afrfq-modal-footer">
				<button type="button" class="button button-secondary afrfq-modal-close">
					<?php esc_html_e( 'Cancel', 'addify_rfq' ); ?>
				</button>
				<button type="button" class="button button-primary" id="import-template-submit" disabled>
					<?php esc_html_e( 'Import Template', 'addify_rfq' ); ?>
				</button>
			</div>
		</div>
	</div>

	<!-- Template Preview Modal -->
	<div id="template-preview-modal" class="afrfq-modal" style="display: none;">
		<div class="afrfq-modal-content">
			<div class="afrfq-modal-header">
				<h3><?php esc_html_e( 'Template Preview', 'addify_rfq' ); ?></h3>
				<button type="button" class="afrfq-modal-close">&times;</button>
			</div>
			<div class="afrfq-modal-body">
				<div id="template-preview-content">
					<!-- Preview content will be loaded here -->
				</div>
			</div>
			<div class="afrfq-modal-footer">
				<button type="button" class="button button-secondary afrfq-modal-close">
					<?php esc_html_e( 'Close', 'addify_rfq' ); ?>
				</button>
				<button type="button" class="button button-primary" id="preview-apply-template">
					<?php esc_html_e( 'Apply This Template', 'addify_rfq' ); ?>
				</button>
			</div>
		</div>
	</div>
</div>

<style>
.afrfq-template-selector {
	margin-top: 20px;
}

.template-filters {
	display: flex;
	gap: 20px;
	margin-bottom: 30px;
	padding: 20px;
	background: #fff;
	border: 1px solid #e1e1e1;
	border-radius: 6px;
	align-items: end;
}

.filter-group {
	display: flex;
	flex-direction: column;
	gap: 5px;
}

.filter-group label {
	font-weight: 600;
	font-size: 13px;
	color: #333;
}

.filter-group select,
.filter-group input[type="text"] {
	padding: 8px 12px;
	border: 1px solid #ddd;
	border-radius: 4px;
	font-size: 14px;
	min-width: 150px;
}

.template-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
	gap: 25px;
}

.template-card {
	background: #fff;
	border: 1px solid #e1e1e1;
	border-radius: 8px;
	overflow: hidden;
	transition: all 0.3s ease;
	position: relative;
}

.template-card:hover {
	border-color: #0073aa;
	box-shadow: 0 4px 12px rgba(0, 115, 170, 0.1);
	transform: translateY(-2px);
}

.template-thumbnail {
	height: 150px;
	background: #f8f9fa;
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
}

.template-thumbnail img {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.template-thumbnail-placeholder {
	color: #999;
}

.template-thumbnail-placeholder .dashicons {
	font-size: 48px;
}

.template-content {
	padding: 20px;
}

.template-title {
	margin: 0 0 10px 0;
	font-size: 16px;
	font-weight: 600;
	color: #333;
}

.template-meta {
	display: flex;
	gap: 10px;
	margin-bottom: 12px;
	font-size: 12px;
	color: #666;
}

.template-meta span {
	background: #f1f1f1;
	padding: 3px 8px;
	border-radius: 12px;
}

.template-description {
	color: #666;
	font-size: 14px;
	line-height: 1.4;
	margin-bottom: 15px;
}

.template-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 5px;
	margin-bottom: 15px;
}

.template-tag {
	background: #e3f2fd;
	color: #1976d2;
	padding: 3px 8px;
	border-radius: 12px;
	font-size: 11px;
	font-weight: 500;
}

.template-actions {
	padding: 15px 20px;
	background: #f8f9fa;
	border-top: 1px solid #e1e1e1;
	display: flex;
	gap: 10px;
	align-items: center;
}

.template-actions .button {
	display: flex;
	align-items: center;
	gap: 5px;
	font-size: 13px;
	padding: 6px 12px;
}

.template-more-actions {
	margin-left: auto;
	position: relative;
}

.template-more-menu {
	position: absolute;
	top: 100%;
	right: 0;
	background: #fff;
	border: 1px solid #e1e1e1;
	border-radius: 4px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	z-index: 1000;
	min-width: 120px;
}

.template-more-menu a,
.template-more-menu button {
	display: flex;
	align-items: center;
	gap: 8px;
	padding: 8px 12px;
	border: none;
	background: none;
	width: 100%;
	text-align: left;
	color: #333;
	text-decoration: none;
	font-size: 13px;
	cursor: pointer;
}

.template-more-menu a:hover,
.template-more-menu button:hover {
	background: #f8f9fa;
}

.template-empty-state {
	grid-column: 1 / -1;
	text-align: center;
	padding: 60px 20px;
	background: #fff;
	border: 1px solid #e1e1e1;
	border-radius: 8px;
}

.empty-state-icon {
	margin-bottom: 20px;
	color: #999;
}

.empty-state-icon .dashicons {
	font-size: 64px;
}

.empty-state-actions {
	margin-top: 20px;
	display: flex;
	gap: 10px;
	justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
	.template-filters {
		flex-direction: column;
		gap: 15px;
	}
	
	.template-grid {
		grid-template-columns: 1fr;
	}
	
	.template-actions {
		flex-direction: column;
		gap: 8px;
	}
	
	.template-actions .button {
		width: 100%;
		justify-content: center;
	}
	
	.template-more-actions {
		margin-left: 0;
		width: 100%;
	}
}
</style>

<script>
jQuery(document).ready(function($) {
	// Template filtering
	$('#template-category-filter, #template-industry-filter').on('change', filterTemplates);
	$('#template-search').on('input', filterTemplates);

	function filterTemplates() {
		var category = $('#template-category-filter').val();
		var industry = $('#template-industry-filter').val();
		var search = $('#template-search').val().toLowerCase();

		$('.template-card').each(function() {
			var $card = $(this);
			var cardCategory = $card.data('category');
			var cardIndustry = $card.data('industry');
			var cardTitle = $card.find('.template-title').text().toLowerCase();
			var cardTags = $card.data('tags').toLowerCase();

			var showCard = true;

			if (category && cardCategory !== category) {
				showCard = false;
			}

			if (industry && cardIndustry !== industry) {
				showCard = false;
			}

			if (search && !cardTitle.includes(search) && !cardTags.includes(search)) {
				showCard = false;
			}

			$card.toggle(showCard);
		});
	}

	// Template more actions menu
	$(document).on('click', '.template-more-btn', function(e) {
		e.stopPropagation();
		$('.template-more-menu').hide();
		$(this).siblings('.template-more-menu').toggle();
	});

	$(document).on('click', function() {
		$('.template-more-menu').hide();
	});

	// File input handler
	$('#import-template-file').on('change', function() {
		$('#import-template-submit').prop('disabled', !this.files.length);
	});

	// Modal handlers
	$('.afrfq-modal-close').on('click', function() {
		$(this).closest('.afrfq-modal').hide();
	});

	$('#import-template-btn').on('click', function() {
		$('#import-template-modal').show();
	});
});
</script>
