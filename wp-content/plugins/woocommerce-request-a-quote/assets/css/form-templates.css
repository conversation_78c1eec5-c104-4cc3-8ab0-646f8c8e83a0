/**
 * Form Templates CSS
 * 
 * Styles for the form templates management interface
 */

/* Template Configuration */
.afrfq-template-config .form-table th {
	width: 200px;
	padding: 15px 10px 15px 0;
	vertical-align: top;
}

.afrfq-template-config .form-table td {
	padding: 15px 10px;
}

.afrfq-template-config .description {
	margin-top: 5px;
	font-style: italic;
	color: #666;
	font-size: 13px;
}

/* Template Fields */
.afrfq-template-fields {
	background: #fff;
	border: 1px solid #e1e1e1;
	border-radius: 6px;
	overflow: hidden;
}

.template-fields-header {
	padding: 20px;
	background: #f8f9fa;
	border-bottom: 1px solid #e1e1e1;
}

.template-fields-header h4 {
	margin: 0 0 10px 0;
	font-size: 16px;
	font-weight: 600;
}

.template-fields-header .description {
	margin: 0 0 15px 0;
	color: #666;
	font-size: 14px;
}

.template-fields-list {
	min-height: 100px;
	padding: 20px;
}

.template-fields-footer {
	padding: 15px 20px;
	background: #f8f9fa;
	border-top: 1px solid #e1e1e1;
	text-align: center;
}

/* Template Field Items */
.template-field-item {
	background: #fff;
	border: 1px solid #e1e1e1;
	border-radius: 6px;
	margin-bottom: 15px;
	transition: all 0.3s ease;
}

.template-field-item:hover {
	border-color: #0073aa;
	box-shadow: 0 2px 8px rgba(0, 115, 170, 0.1);
}

.template-field-item.ui-sortable-helper {
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
	transform: rotate(2deg);
}

.field-placeholder {
	background: #f0f8ff;
	border: 2px dashed #0073aa;
	border-radius: 6px;
	height: 60px;
	margin-bottom: 15px;
}

/* Field Header */
.field-header {
	display: flex;
	align-items: center;
	padding: 15px 20px;
	background: #f8f9fa;
	border-bottom: 1px solid #e1e1e1;
	cursor: pointer;
}

.field-handle {
	color: #999;
	margin-right: 10px;
	cursor: grab;
	font-size: 16px;
}

.field-handle:active {
	cursor: grabbing;
}

.field-title {
	font-weight: 600;
	color: #333;
	margin-right: 8px;
}

.field-type {
	color: #666;
	font-size: 13px;
	font-style: italic;
}

.field-actions {
	margin-left: auto;
	display: flex;
	gap: 5px;
}

.field-actions button {
	background: none;
	border: none;
	padding: 5px;
	cursor: pointer;
	border-radius: 3px;
	transition: background-color 0.2s ease;
}

.field-actions button:hover {
	background: rgba(0, 0, 0, 0.1);
}

.field-actions .remove-field:hover {
	background: rgba(220, 53, 69, 0.1);
	color: #dc3545;
}

/* Field Configuration */
.field-config {
	padding: 20px;
	background: #fff;
}

.field-config-table {
	width: 100%;
	border-collapse: collapse;
}

.field-config-table td {
	padding: 10px 15px 10px 0;
	vertical-align: top;
	width: 33.33%;
}

.field-config-table td:last-child {
	padding-right: 0;
}

.field-config-table label {
	display: block;
	font-weight: 600;
	margin-bottom: 5px;
	color: #333;
	font-size: 13px;
}

.field-config-table input,
.field-config-table select,
.field-config-table textarea {
	width: 100%;
	padding: 8px 12px;
	border: 1px solid #ddd;
	border-radius: 4px;
	font-size: 14px;
	transition: border-color 0.2s ease;
}

.field-config-table input:focus,
.field-config-table select:focus,
.field-config-table textarea:focus {
	border-color: #0073aa;
	outline: none;
	box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.1);
}

.field-config-table textarea {
	resize: vertical;
	min-height: 60px;
}

/* Responsive Design */
@media (max-width: 768px) {
	.field-config-table,
	.field-config-table tbody,
	.field-config-table tr,
	.field-config-table td {
		display: block;
		width: 100%;
	}
	
	.field-config-table td {
		padding: 10px 0;
		border-bottom: 1px solid #f1f1f1;
	}
	
	.field-header {
		flex-wrap: wrap;
		gap: 10px;
	}
	
	.field-actions {
		margin-left: 0;
		width: 100%;
		justify-content: flex-end;
	}
}

/* Button Styles */
.add-template-field {
	display: inline-flex;
	align-items: center;
	gap: 5px;
}

.button.loading {
	opacity: 0.7;
	pointer-events: none;
	position: relative;
}

.button.loading::before {
	content: '';
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	width: 16px;
	height: 16px;
	border: 2px solid transparent;
	border-top: 2px solid currentColor;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: translate(-50%, -50%) rotate(0deg); }
	100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Template Actions Sidebar */
.afrfq-template-actions {
	background: #fff;
	border: 1px solid #e1e1e1;
	border-radius: 6px;
}

.template-action-section {
	padding: 20px;
	border-bottom: 1px solid #f1f1f1;
}

.template-action-section:last-child {
	border-bottom: none;
}

.template-action-section h4 {
	margin: 0 0 15px 0;
	font-size: 14px;
	font-weight: 600;
	color: #333;
}

.action-buttons {
	display: flex;
	flex-direction: column;
	gap: 10px;
}

.action-buttons .button {
	display: flex;
	align-items: center;
	gap: 8px;
	justify-content: flex-start;
	text-align: left;
	padding: 10px 15px;
}

.import-section {
	margin-top: 15px;
}

.template-stats {
	display: flex;
	flex-direction: column;
	gap: 10px;
}

.stat-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 8px 0;
	border-bottom: 1px solid #f1f1f1;
	font-size: 13px;
}

.stat-item:last-child {
	border-bottom: none;
}

.stat-item strong {
	color: #333;
}

.stat-item span {
	color: #666;
}

/* Template List (if needed for template selection) */
.template-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
	gap: 20px;
	margin: 20px 0;
}

.template-card {
	background: #fff;
	border: 1px solid #e1e1e1;
	border-radius: 8px;
	padding: 20px;
	transition: all 0.3s ease;
	cursor: pointer;
}

.template-card:hover {
	border-color: #0073aa;
	box-shadow: 0 4px 12px rgba(0, 115, 170, 0.1);
	transform: translateY(-2px);
}

.template-card h3 {
	margin: 0 0 10px 0;
	font-size: 16px;
	color: #333;
}

.template-card .template-meta {
	display: flex;
	gap: 15px;
	margin-bottom: 10px;
	font-size: 12px;
	color: #666;
}

.template-card .template-description {
	color: #666;
	font-size: 14px;
	line-height: 1.4;
	margin-bottom: 15px;
}

.template-card .template-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 5px;
}

.template-tag {
	background: #f1f1f1;
	color: #666;
	padding: 3px 8px;
	border-radius: 12px;
	font-size: 11px;
}

/* Utility Classes */
.text-center {
	text-align: center;
}

.text-muted {
	color: #666;
}

.mb-0 {
	margin-bottom: 0;
}

.mt-15 {
	margin-top: 15px;
}

.hidden {
	display: none;
}
