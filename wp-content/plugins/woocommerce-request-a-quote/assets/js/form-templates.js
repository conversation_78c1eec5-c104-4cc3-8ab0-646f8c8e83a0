/**
 * Form Templates JavaScript
 * 
 * Handles template management functionality including drag-and-drop,
 * field configuration, preview, import/export, and template application.
 */

jQuery(document).ready(function($) {
	'use strict';

	// Initialize template functionality
	initializeTemplateFields();
	initializeTemplateActions();

	/**
	 * Initialize template fields functionality
	 */
	function initializeTemplateFields() {
		// Make fields sortable
		if ($('#template-fields-sortable').length) {
			$('#template-fields-sortable').sortable({
				handle: '.field-handle',
				placeholder: 'field-placeholder',
				update: function(event, ui) {
					updateFieldOrder();
				}
			});
		}

		// Add new field button
		$(document).on('click', '.add-template-field', function() {
			addNewTemplateField();
		});

		// Remove field button
		$(document).on('click', '.remove-field', function() {
			if (confirm('Are you sure you want to remove this field?')) {
				$(this).closest('.template-field-item').remove();
				updateFieldOrder();
			}
		});

		// Toggle field configuration
		$(document).on('click', '.toggle-field-config', function() {
			var $config = $(this).closest('.template-field-item').find('.field-config');
			var $icon = $(this).find('.dashicons');
			
			$config.slideToggle();
			$icon.toggleClass('dashicons-arrow-down-alt2 dashicons-arrow-up-alt2');
		});

		// Field type change handler
		$(document).on('change', '.field-type-select', function() {
			var fieldType = $(this).val();
			var $fieldItem = $(this).closest('.template-field-item');
			
			updateFieldTypeDisplay($fieldItem, fieldType);
			updateFieldTitle($fieldItem);
		});

		// Field label change handler
		$(document).on('input', '.field-label-input', function() {
			updateFieldTitle($(this).closest('.template-field-item'));
		});

		// Field name change handler
		$(document).on('input', '.field-name-input', function() {
			var value = $(this).val();
			// Auto-generate field name from label if empty
			if (!value) {
				var label = $(this).closest('.template-field-item').find('.field-label-input').val();
				if (label) {
					value = generateFieldName(label);
					$(this).val(value);
				}
			}
		});
	}

	/**
	 * Initialize template actions functionality
	 */
	function initializeTemplateActions() {
		// Preview template
		$(document).on('click', '.template-preview-btn', function() {
			var templateId = $(this).data('template-id');
			previewTemplate(templateId);
		});

		// Export template
		$(document).on('click', '.template-export-btn', function() {
			var templateId = $(this).data('template-id');
			exportTemplate(templateId);
		});

		// Apply template
		$(document).on('click', '.template-apply-btn', function() {
			var templateId = $(this).data('template-id');
			if (confirm(afrfq_templates.strings.confirm_apply)) {
				applyTemplate(templateId);
			}
		});

		// Import template
		$(document).on('click', '.template-import-btn', function() {
			importTemplate();
		});

		// Duplicate template
		$(document).on('click', '.template-duplicate-btn', function() {
			var templateId = $(this).data('template-id');
			if (confirm('Are you sure you want to duplicate this template?')) {
				duplicateTemplate(templateId);
			}
		});
	}

	/**
	 * Add new template field
	 */
	function addNewTemplateField() {
		var template = $('#template-field-template').html();
		var index = $('.template-field-item').length;
		var order = index + 1;
		
		template = template.replace(/\{\{INDEX\}\}/g, index);
		template = template.replace(/\{\{ORDER\}\}/g, order);
		
		$('#template-fields-sortable').append(template);
		
		// Show the configuration for the new field
		var $newField = $('.template-field-item').last();
		$newField.find('.field-config').show();
		$newField.find('.toggle-field-config .dashicons').removeClass('dashicons-arrow-down-alt2').addClass('dashicons-arrow-up-alt2');
		
		// Focus on the field name input
		$newField.find('.field-name-input').focus();
	}

	/**
	 * Update field order based on current position
	 */
	function updateFieldOrder() {
		$('.template-field-item').each(function(index) {
			$(this).find('.field-order-input').val(index + 1);
		});
	}

	/**
	 * Update field type specific display
	 */
	function updateFieldTypeDisplay($fieldItem, fieldType) {
		var $optionsRow = $fieldItem.find('.field-options-row');
		var $fileTypesRow = $fieldItem.find('.field-file-types-row');
		var $validationRow = $fieldItem.find('.field-validation-row');

		// Hide all type-specific rows
		$optionsRow.hide();
		$fileTypesRow.hide();
		$validationRow.hide();

		// Show relevant rows based on field type
		if (['select', 'radio', 'checkbox'].includes(fieldType)) {
			$optionsRow.show();
		}

		if (fieldType === 'file') {
			$fileTypesRow.show();
		}

		if (['text', 'textarea', 'number'].includes(fieldType)) {
			$validationRow.show();
		}
	}

	/**
	 * Update field title in header
	 */
	function updateFieldTitle($fieldItem) {
		var label = $fieldItem.find('.field-label-input').val() || 'New Field';
		var type = $fieldItem.find('.field-type-select').val() || 'text';
		
		$fieldItem.find('.field-title').text(label);
		$fieldItem.find('.field-type').text('(' + type + ')');
	}

	/**
	 * Generate field name from label
	 */
	function generateFieldName(label) {
		return label.toLowerCase()
			.replace(/[^a-z0-9\s]/g, '')
			.replace(/\s+/g, '_')
			.substring(0, 50);
	}

	/**
	 * Preview template
	 */
	function previewTemplate(templateId) {
		var $button = $('.template-preview-btn');
		$button.addClass('loading');

		$.ajax({
			url: afrfq_templates.ajax_url,
			type: 'POST',
			data: {
				action: 'afrfq_preview_template',
				template_id: templateId,
				nonce: afrfq_templates.nonce
			},
			success: function(response) {
				if (response.success) {
					$('#template-preview-content').html(response.data.preview_html);
					$('#template-preview-modal').show();
				} else {
					alert(response.data || afrfq_templates.strings.error_occurred);
				}
			},
			error: function() {
				alert(afrfq_templates.strings.error_occurred);
			},
			complete: function() {
				$button.removeClass('loading');
			}
		});
	}

	/**
	 * Export template
	 */
	function exportTemplate(templateId) {
		var $button = $('.template-export-btn');
		$button.addClass('loading');

		$.ajax({
			url: afrfq_templates.ajax_url,
			type: 'POST',
			data: {
				action: 'afrfq_export_template',
				template_id: templateId,
				nonce: afrfq_templates.nonce
			},
			success: function(response) {
				if (response.success) {
					// Create and trigger download
					var blob = new Blob([JSON.stringify(response.data.data, null, 2)], {
						type: 'application/json'
					});
					var url = window.URL.createObjectURL(blob);
					var a = document.createElement('a');
					a.href = url;
					a.download = response.data.filename;
					document.body.appendChild(a);
					a.click();
					document.body.removeChild(a);
					window.URL.revokeObjectURL(url);
					
					alert(afrfq_templates.strings.template_exported);
				} else {
					alert(response.data || afrfq_templates.strings.error_occurred);
				}
			},
			error: function() {
				alert(afrfq_templates.strings.error_occurred);
			},
			complete: function() {
				$button.removeClass('loading');
			}
		});
	}

	/**
	 * Apply template
	 */
	function applyTemplate(templateId) {
		var $button = $('.template-apply-btn');
		$button.addClass('loading');

		$.ajax({
			url: afrfq_templates.ajax_url,
			type: 'POST',
			data: {
				action: 'afrfq_apply_template',
				template_id: templateId,
				target_type: 'new',
				nonce: afrfq_templates.nonce
			},
			success: function(response) {
				if (response.success) {
					alert(afrfq_templates.strings.template_applied);
					// Redirect to fields list
					window.location.href = 'edit.php?post_type=addify_rfq_fields';
				} else {
					alert(response.data || afrfq_templates.strings.error_occurred);
				}
			},
			error: function() {
				alert(afrfq_templates.strings.error_occurred);
			},
			complete: function() {
				$button.removeClass('loading');
			}
		});
	}

	/**
	 * Import template
	 */
	function importTemplate() {
		var fileInput = document.getElementById('template-import-file');
		var file = fileInput.files[0];
		
		if (!file) {
			alert('Please select a file to import.');
			return;
		}

		var formData = new FormData();
		formData.append('action', 'afrfq_import_template');
		formData.append('template_file', file);
		formData.append('nonce', afrfq_templates.nonce);

		var $button = $('.template-import-btn');
		$button.addClass('loading');

		$.ajax({
			url: afrfq_templates.ajax_url,
			type: 'POST',
			data: formData,
			processData: false,
			contentType: false,
			success: function(response) {
				if (response.success) {
					alert(afrfq_templates.strings.template_imported);
					// Redirect to the imported template
					window.location.href = 'post.php?post=' + response.data.template_id + '&action=edit';
				} else {
					alert(response.data || afrfq_templates.strings.error_occurred);
				}
			},
			error: function() {
				alert(afrfq_templates.strings.error_occurred);
			},
			complete: function() {
				$button.removeClass('loading');
				// Reset file input
				fileInput.value = '';
				$('#selected-file-name').text('');
				$button.prop('disabled', true);
			}
		});
	}

	/**
	 * Duplicate template
	 */
	function duplicateTemplate(templateId) {
		var $button = $('.template-duplicate-btn[data-template-id="' + templateId + '"]');
		$button.addClass('loading');

		$.ajax({
			url: afrfq_templates.ajax_url,
			type: 'POST',
			data: {
				action: 'afrfq_duplicate_template',
				template_id: templateId,
				nonce: afrfq_templates.nonce
			},
			success: function(response) {
				if (response.success) {
					alert('Template duplicated successfully!');
					// Redirect to edit the new template
					window.location.href = response.data.edit_url;
				} else {
					alert(response.data || afrfq_templates.strings.error_occurred);
				}
			},
			error: function() {
				alert(afrfq_templates.strings.error_occurred);
			},
			complete: function() {
				$button.removeClass('loading');
			}
		});
	}
});
