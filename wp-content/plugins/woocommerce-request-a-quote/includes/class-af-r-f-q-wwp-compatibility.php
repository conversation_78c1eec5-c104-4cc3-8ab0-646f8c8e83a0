<?php
/**
 * WooCommerce Wholesale Prices Compatibility Class
 *
 * Handles integration between Request a Quote plugin and WooCommerce Wholesale Prices plugin.
 *
 * @package addify-request-a-quote
 * @version 1.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Restrict direct access.
}

if ( ! class_exists( 'AF_R_F_Q_WWP_Compatibility' ) ) {

	/**
	 * AF_R_F_Q_WWP_Compatibility
	 */
	class AF_R_F_Q_WWP_Compatibility {

		/**
		 * Wholesale roles instance
		 *
		 * @var object
		 */
		private $wholesale_roles;

		/**
		 * Constructor
		 */
		public function __construct() {
			// Initialize wholesale roles
			if ( class_exists( 'WWP_Wholesale_Roles' ) ) {
				$this->wholesale_roles = WWP_Wholesale_Roles::getInstance();
			}

			// Hook into quote session changes to recalculate wholesale prices
			add_action( 'addify_quote_session_changed', array( $this, 'recalculate_wholesale_prices' ), 20 );
			
			// Hook into quote creation to ensure wholesale prices are preserved
			add_action( 'addify_quote_created', array( $this, 'preserve_wholesale_prices_in_quote' ), 10, 1 );
			
			// Debug logging
			error_log( 'WWP Compatibility class loaded' );
		}

		/**
		 * Recalculate wholesale prices when quote session changes
		 */
		public function recalculate_wholesale_prices() {
			if ( ! WC()->session ) {
				return;
			}

			$quote_contents = WC()->session->get( 'quotes' );
			
			if ( empty( $quote_contents ) || ! is_array( $quote_contents ) ) {
				return;
			}

			$user = wp_get_current_user();
			$user_wholesale_roles = array();

			// Get user wholesale roles
			if ( $this->wholesale_roles && is_user_logged_in() ) {
				$user_wholesale_roles = $this->wholesale_roles->getUserWholesaleRole();
			}

			error_log( 'Recalculating wholesale prices for user roles: ' . print_r( $user_wholesale_roles, true ) );

			// Process each quote item
			foreach ( $quote_contents as $quote_item_key => $quote_item ) {
				if ( ! isset( $quote_item['data'] ) || ! is_object( $quote_item['data'] ) ) {
					continue;
				}

				$product = $quote_item['data'];
				$product_id = $product->get_id();
				$quantity = isset( $quote_item['quantity'] ) ? intval( $quote_item['quantity'] ) : 1;

				// Get wholesale price if user has wholesale role
				if ( ! empty( $user_wholesale_roles ) ) {
					$wholesale_price = $this->get_wholesale_price( $product, $user_wholesale_roles, $quantity );
					
					if ( $wholesale_price !== false && $wholesale_price > 0 ) {
						// Apply wholesale price
						$product->set_price( $wholesale_price );
						$quote_contents[ $quote_item_key ]['wholesale_price'] = $wholesale_price;
						$quote_contents[ $quote_item_key ]['role_base_price'] = $wholesale_price;
						
						error_log( "Applied wholesale price {$wholesale_price} to product {$product_id}" );
					}
				}
			}

			// Update session with modified quote contents
			WC()->session->set( 'quotes', $quote_contents );
		}

		/**
		 * Get wholesale price for a product
		 *
		 * @param WC_Product $product Product object.
		 * @param array      $user_wholesale_roles User wholesale roles.
		 * @param int        $quantity Quantity.
		 * @return float|false Wholesale price or false if not available.
		 */
		private function get_wholesale_price( $product, $user_wholesale_roles, $quantity = 1 ) {
			if ( empty( $user_wholesale_roles ) || ! is_array( $user_wholesale_roles ) ) {
				return false;
			}

			// Get the first wholesale role (primary role)
			$wholesale_role = $user_wholesale_roles[0];
			
			// Check if WWP wholesale prices class exists and get wholesale price
			if ( class_exists( 'WWP_Wholesale_Prices' ) ) {
				$wwp_wholesale_prices = WWP_Wholesale_Prices::instance();
				
				// Get wholesale price using WWP method
				$wholesale_price_data = $wwp_wholesale_prices->getProductWholesalePrice( $product->get_id(), array( $wholesale_role ) );
				
				if ( ! empty( $wholesale_price_data['wholesale_price'] ) ) {
					return floatval( $wholesale_price_data['wholesale_price'] );
				}
			}

			// Fallback: Check meta directly
			$wholesale_price_meta_key = $wholesale_role . '_wholesale_price';
			$wholesale_price = get_post_meta( $product->get_id(), $wholesale_price_meta_key, true );
			
			if ( ! empty( $wholesale_price ) && is_numeric( $wholesale_price ) ) {
				return floatval( $wholesale_price );
			}

			return false;
		}

		/**
		 * Preserve wholesale prices when quote is created
		 *
		 * @param int $quote_id Quote ID.
		 */
		public function preserve_wholesale_prices_in_quote( $quote_id ) {
			if ( ! $quote_id ) {
				return;
			}

			$quote_contents = get_post_meta( $quote_id, 'quote_contents', true );
			
			if ( empty( $quote_contents ) || ! is_array( $quote_contents ) ) {
				return;
			}

			$user_id = get_post_meta( $quote_id, '_customer_user', true );
			$user_wholesale_roles = array();

			// Get wholesale roles for the quote customer
			if ( $this->wholesale_roles && $user_id ) {
				$user = get_user_by( 'id', $user_id );
				if ( $user ) {
					$user_wholesale_roles = $this->wholesale_roles->getUserWholesaleRole( $user );
				}
			}

			// Add wholesale role information to quote meta
			if ( ! empty( $user_wholesale_roles ) ) {
				update_post_meta( $quote_id, '_wholesale_customer_roles', $user_wholesale_roles );
				update_post_meta( $quote_id, '_is_wholesale_quote', 'yes' );
				
				error_log( "Quote {$quote_id} marked as wholesale quote for roles: " . implode( ', ', $user_wholesale_roles ) );
			}

			// Ensure wholesale prices are preserved in quote contents
			$updated_contents = array();
			foreach ( $quote_contents as $quote_item_key => $quote_item ) {
				if ( isset( $quote_item['wholesale_price'] ) || isset( $quote_item['role_base_price'] ) ) {
					$quote_item['_wholesale_price_applied'] = true;
				}
				$updated_contents[ $quote_item_key ] = $quote_item;
			}

			// Update quote contents with wholesale price flags
			update_post_meta( $quote_id, 'quote_contents', $updated_contents );
		}

		/**
		 * Check if current user has wholesale role
		 *
		 * @return bool
		 */
		public function is_wholesale_customer() {
			if ( ! $this->wholesale_roles || ! is_user_logged_in() ) {
				return false;
			}

			$user_wholesale_roles = $this->wholesale_roles->getUserWholesaleRole();
			return ! empty( $user_wholesale_roles );
		}

		/**
		 * Get current user's wholesale roles
		 *
		 * @return array
		 */
		public function get_user_wholesale_roles() {
			if ( ! $this->wholesale_roles || ! is_user_logged_in() ) {
				return array();
			}

			return $this->wholesale_roles->getUserWholesaleRole();
		}
	}

	// Initialize the compatibility class
	new AF_R_F_Q_WWP_Compatibility();
}
