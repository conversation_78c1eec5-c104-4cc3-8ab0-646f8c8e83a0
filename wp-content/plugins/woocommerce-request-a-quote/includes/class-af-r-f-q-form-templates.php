<?php
/**
 * Addify Form Templates System
 *
 * Manages form templates for creating pre-built quote forms for different industries and use cases.
 *
 * @package addify-request-a-quote
 * @version 1.6.0
 */

defined( 'ABSPATH' ) || exit;

/**
 * AF_R_F_Q_Form_Templates class.
 */
class AF_R_F_Q_Form_Templates {

	/**
	 * Template post type name.
	 *
	 * @var string
	 */
	const POST_TYPE = 'afrfq_form_template';

	/**
	 * Constructor.
	 */
	public function __construct() {
		add_action( 'init', array( $this, 'register_post_type' ) );
		add_action( 'add_meta_boxes', array( $this, 'add_meta_boxes' ) );
		add_action( 'save_post_' . self::POST_TYPE, array( $this, 'save_template_meta' ) );
		add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_scripts' ) );
		add_action( 'wp_ajax_afrfq_apply_template', array( $this, 'ajax_apply_template' ) );
		add_action( 'wp_ajax_afrfq_export_template', array( $this, 'ajax_export_template' ) );
		add_action( 'wp_ajax_afrfq_import_template', array( $this, 'ajax_import_template' ) );
		add_action( 'wp_ajax_afrfq_preview_template', array( $this, 'ajax_preview_template' ) );
		add_action( 'wp_ajax_afrfq_duplicate_template', array( $this, 'ajax_duplicate_template' ) );
		
		// Initialize default templates
		add_action( 'init', array( $this, 'create_default_templates' ), 20 );
	}

	/**
	 * Register the form template post type.
	 */
	public function register_post_type() {
		$labels = array(
			'name'                  => _x( 'Form Templates', 'Post Type General Name', 'addify_rfq' ),
			'singular_name'         => _x( 'Form Template', 'Post Type Singular Name', 'addify_rfq' ),
			'menu_name'             => __( 'Form Templates', 'addify_rfq' ),
			'name_admin_bar'        => __( 'Form Template', 'addify_rfq' ),
			'archives'              => __( 'Template Archives', 'addify_rfq' ),
			'attributes'            => __( 'Template Attributes', 'addify_rfq' ),
			'parent_item_colon'     => __( 'Parent Template:', 'addify_rfq' ),
			'all_items'             => __( 'All Templates', 'addify_rfq' ),
			'add_new_item'          => __( 'Add New Template', 'addify_rfq' ),
			'add_new'               => __( 'Add New', 'addify_rfq' ),
			'new_item'              => __( 'New Template', 'addify_rfq' ),
			'edit_item'             => __( 'Edit Template', 'addify_rfq' ),
			'update_item'           => __( 'Update Template', 'addify_rfq' ),
			'view_item'             => __( 'View Template', 'addify_rfq' ),
			'view_items'            => __( 'View Templates', 'addify_rfq' ),
			'search_items'          => __( 'Search Template', 'addify_rfq' ),
			'not_found'             => __( 'Not found', 'addify_rfq' ),
			'not_found_in_trash'    => __( 'Not found in Trash', 'addify_rfq' ),
			'featured_image'        => __( 'Template Preview', 'addify_rfq' ),
			'set_featured_image'    => __( 'Set template preview', 'addify_rfq' ),
			'remove_featured_image' => __( 'Remove template preview', 'addify_rfq' ),
			'use_featured_image'    => __( 'Use as template preview', 'addify_rfq' ),
			'insert_into_item'      => __( 'Insert into template', 'addify_rfq' ),
			'uploaded_to_this_item' => __( 'Uploaded to this template', 'addify_rfq' ),
			'items_list'            => __( 'Templates list', 'addify_rfq' ),
			'items_list_navigation' => __( 'Templates list navigation', 'addify_rfq' ),
			'filter_items_list'     => __( 'Filter templates list', 'addify_rfq' ),
		);

		$args = array(
			'label'                 => __( 'Form Template', 'addify_rfq' ),
			'description'           => __( 'Form templates for quote requests', 'addify_rfq' ),
			'labels'                => $labels,
			'supports'              => array( 'title', 'editor', 'thumbnail', 'custom-fields' ),
			'taxonomies'            => array(),
			'hierarchical'          => false,
			'public'                => false,
			'show_ui'               => true,
			'show_in_menu'          => 'edit.php?post_type=addify_rfq_fields',
			'menu_position'         => 5,
			'show_in_admin_bar'     => false,
			'show_in_nav_menus'     => false,
			'can_export'            => true,
			'has_archive'           => false,
			'exclude_from_search'   => true,
			'publicly_queryable'    => false,
			'capability_type'       => 'post',
			'show_in_rest'          => false,
		);

		register_post_type( self::POST_TYPE, $args );
	}

	/**
	 * Add meta boxes for template configuration.
	 */
	public function add_meta_boxes() {
		add_meta_box(
			'afrfq-template-config',
			__( 'Template Configuration', 'addify_rfq' ),
			array( $this, 'template_config_callback' ),
			self::POST_TYPE,
			'normal',
			'high'
		);

		add_meta_box(
			'afrfq-template-fields',
			__( 'Template Fields', 'addify_rfq' ),
			array( $this, 'template_fields_callback' ),
			self::POST_TYPE,
			'normal',
			'high'
		);

		add_meta_box(
			'afrfq-template-actions',
			__( 'Template Actions', 'addify_rfq' ),
			array( $this, 'template_actions_callback' ),
			self::POST_TYPE,
			'side',
			'high'
		);
	}

	/**
	 * Template configuration meta box callback.
	 */
	public function template_config_callback( $post ) {
		wp_nonce_field( 'afrfq_template_meta', 'afrfq_template_nonce' );
		
		$template_category = get_post_meta( $post->ID, '_afrfq_template_category', true );
		$template_industry = get_post_meta( $post->ID, '_afrfq_template_industry', true );
		$template_description = get_post_meta( $post->ID, '_afrfq_template_description', true );
		$template_tags = get_post_meta( $post->ID, '_afrfq_template_tags', true );
		$template_version = get_post_meta( $post->ID, '_afrfq_template_version', true ) ?: '1.0.0';
		$template_author = get_post_meta( $post->ID, '_afrfq_template_author', true ) ?: get_current_user()->display_name;
		
		include AFRFQ_PLUGIN_DIR . 'admin/meta-boxes/templates/template-config.php';
	}

	/**
	 * Template fields meta box callback.
	 */
	public function template_fields_callback( $post ) {
		$template_fields = get_post_meta( $post->ID, '_afrfq_template_fields', true );
		if ( ! is_array( $template_fields ) ) {
			$template_fields = array();
		}
		
		include AFRFQ_PLUGIN_DIR . 'admin/meta-boxes/templates/template-fields.php';
	}

	/**
	 * Template actions meta box callback.
	 */
	public function template_actions_callback( $post ) {
		include AFRFQ_PLUGIN_DIR . 'admin/meta-boxes/templates/template-actions.php';
	}

	/**
	 * Save template meta data.
	 */
	public function save_template_meta( $post_id ) {
		// Verify nonce
		if ( ! isset( $_POST['afrfq_template_nonce'] ) || ! wp_verify_nonce( $_POST['afrfq_template_nonce'], 'afrfq_template_meta' ) ) {
			return;
		}

		// Check if user has permission to edit
		if ( ! current_user_can( 'edit_post', $post_id ) ) {
			return;
		}

		// Save template configuration
		$fields_to_save = array(
			'_afrfq_template_category',
			'_afrfq_template_industry',
			'_afrfq_template_description',
			'_afrfq_template_tags',
			'_afrfq_template_version',
			'_afrfq_template_author',
			'_afrfq_template_fields',
		);

		foreach ( $fields_to_save as $field ) {
			if ( isset( $_POST[ $field ] ) ) {
				$value = $_POST[ $field ];
				
				// Sanitize based on field type
				if ( $field === '_afrfq_template_fields' ) {
					$value = $this->sanitize_template_fields( $value );
				} else {
					$value = sanitize_text_field( $value );
				}
				
				update_post_meta( $post_id, $field, $value );
			}
		}
	}

	/**
	 * Sanitize template fields data.
	 */
	private function sanitize_template_fields( $fields ) {
		if ( ! is_array( $fields ) ) {
			return array();
		}

		$sanitized = array();
		foreach ( $fields as $field ) {
			if ( is_array( $field ) ) {
				$sanitized_field = array();
				foreach ( $field as $key => $value ) {
					$sanitized_field[ sanitize_key( $key ) ] = sanitize_text_field( $value );
				}
				$sanitized[] = $sanitized_field;
			}
		}

		return $sanitized;
	}

	/**
	 * Enqueue admin scripts and styles.
	 */
	public function enqueue_admin_scripts( $hook ) {
		global $post_type;
		
		if ( $post_type === self::POST_TYPE || ( isset( $_GET['post_type'] ) && $_GET['post_type'] === 'addify_rfq_fields' ) ) {
			wp_enqueue_script( 'jquery-ui-sortable' );
			wp_enqueue_script( 'jquery-ui-draggable' );
			wp_enqueue_script( 'jquery-ui-droppable' );
			
			wp_enqueue_script(
				'afrfq-form-templates',
				AFRFQ_PLUGIN_URL . 'assets/js/form-templates.js',
				array( 'jquery', 'jquery-ui-sortable' ),
				AFRFQ_PLUGIN_VERSION,
				true
			);

			wp_enqueue_style(
				'afrfq-form-templates',
				AFRFQ_PLUGIN_URL . 'assets/css/form-templates.css',
				array(),
				AFRFQ_PLUGIN_VERSION
			);

			wp_localize_script( 'afrfq-form-templates', 'afrfq_templates', array(
				'ajax_url' => admin_url( 'admin-ajax.php' ),
				'nonce' => wp_create_nonce( 'afrfq_templates_nonce' ),
				'strings' => array(
					'confirm_apply' => __( 'Are you sure you want to apply this template? This will replace all existing fields.', 'addify_rfq' ),
					'template_applied' => __( 'Template applied successfully!', 'addify_rfq' ),
					'template_exported' => __( 'Template exported successfully!', 'addify_rfq' ),
					'template_imported' => __( 'Template imported successfully!', 'addify_rfq' ),
					'error_occurred' => __( 'An error occurred. Please try again.', 'addify_rfq' ),
				),
			) );
		}
	}

	/**
	 * Get all available templates.
	 */
	public function get_templates( $args = array() ) {
		$default_args = array(
			'post_type' => self::POST_TYPE,
			'post_status' => 'publish',
			'posts_per_page' => -1,
			'orderby' => 'title',
			'order' => 'ASC',
		);

		$args = wp_parse_args( $args, $default_args );
		$templates = get_posts( $args );

		$formatted_templates = array();
		foreach ( $templates as $template ) {
			$formatted_templates[] = $this->format_template_data( $template );
		}

		return $formatted_templates;
	}

	/**
	 * Format template data for display.
	 */
	private function format_template_data( $template ) {
		return array(
			'id' => $template->ID,
			'title' => $template->post_title,
			'description' => get_post_meta( $template->ID, '_afrfq_template_description', true ),
			'category' => get_post_meta( $template->ID, '_afrfq_template_category', true ),
			'industry' => get_post_meta( $template->ID, '_afrfq_template_industry', true ),
			'tags' => get_post_meta( $template->ID, '_afrfq_template_tags', true ),
			'version' => get_post_meta( $template->ID, '_afrfq_template_version', true ),
			'author' => get_post_meta( $template->ID, '_afrfq_template_author', true ),
			'fields' => get_post_meta( $template->ID, '_afrfq_template_fields', true ),
			'thumbnail' => get_the_post_thumbnail_url( $template->ID, 'medium' ),
			'created' => $template->post_date,
			'modified' => $template->post_modified,
		);
	}

	/**
	 * AJAX handler for applying a template.
	 */
	public function ajax_apply_template() {
		check_ajax_referer( 'afrfq_templates_nonce', 'nonce' );

		if ( ! current_user_can( 'manage_options' ) ) {
			wp_die( __( 'You do not have sufficient permissions to access this page.', 'addify_rfq' ) );
		}

		$template_id = intval( $_POST['template_id'] );
		$target_type = sanitize_text_field( $_POST['target_type'] ?? 'new' );

		if ( ! $template_id ) {
			wp_send_json_error( __( 'Invalid template ID.', 'addify_rfq' ) );
		}

		$template_fields = get_post_meta( $template_id, '_afrfq_template_fields', true );
		if ( ! is_array( $template_fields ) ) {
			wp_send_json_error( __( 'Template has no fields configured.', 'addify_rfq' ) );
		}

		$result = $this->apply_template_fields( $template_fields, $target_type );

		if ( $result ) {
			wp_send_json_success( array(
				'message' => __( 'Template applied successfully!', 'addify_rfq' ),
				'created_fields' => $result,
			) );
		} else {
			wp_send_json_error( __( 'Failed to apply template.', 'addify_rfq' ) );
		}
	}

	/**
	 * Apply template fields to create new form fields.
	 */
	private function apply_template_fields( $template_fields, $target_type = 'new' ) {
		$created_fields = array();

		foreach ( $template_fields as $field_config ) {
			$field_id = wp_insert_post( array(
				'post_type' => 'addify_rfq_fields',
				'post_title' => $field_config['label'] ?? 'Template Field',
				'post_status' => 'publish',
				'menu_order' => $field_config['order'] ?? 0,
			) );

			if ( ! is_wp_error( $field_id ) ) {
				// Save field meta data
				$meta_fields = array(
					'afrfq_field_name' => $field_config['name'] ?? 'field_' . $field_id,
					'afrfq_field_type' => $field_config['type'] ?? 'text',
					'afrfq_field_label' => $field_config['label'] ?? '',
					'afrfq_field_placeholder' => $field_config['placeholder'] ?? '',
					'afrfq_field_required' => $field_config['required'] ?? 'no',
					'afrfq_field_description' => $field_config['description'] ?? '',
					'afrfq_field_width' => $field_config['width'] ?? 'full_width',
					'afrfq_field_enable' => 'enable',
					'afrfq_field_options' => $field_config['options'] ?? array(),
					'afrfq_file_types' => $field_config['file_types'] ?? '',
					'afrfq_file_size' => $field_config['file_size'] ?? '10000000',
					'afrfq_field_min_length' => $field_config['min_length'] ?? '',
					'afrfq_field_max_length' => $field_config['max_length'] ?? '',
					'afrfq_field_min_value' => $field_config['min_value'] ?? '',
					'afrfq_field_max_value' => $field_config['max_value'] ?? '',
					'afrfq_field_pattern' => $field_config['pattern'] ?? '',
					'afrfq_field_css_class' => $field_config['css_class'] ?? '',
				);

				foreach ( $meta_fields as $meta_key => $meta_value ) {
					update_post_meta( $field_id, $meta_key, $meta_value );
				}

				$created_fields[] = $field_id;
			}
		}

		return $created_fields;
	}

	/**
	 * AJAX handler for exporting a template.
	 */
	public function ajax_export_template() {
		check_ajax_referer( 'afrfq_templates_nonce', 'nonce' );

		if ( ! current_user_can( 'manage_options' ) ) {
			wp_die( __( 'You do not have sufficient permissions to access this page.', 'addify_rfq' ) );
		}

		$template_id = intval( $_POST['template_id'] );
		if ( ! $template_id ) {
			wp_send_json_error( __( 'Invalid template ID.', 'addify_rfq' ) );
		}

		$template_data = $this->export_template( $template_id );
		if ( $template_data ) {
			wp_send_json_success( array(
				'data' => $template_data,
				'filename' => sanitize_file_name( get_the_title( $template_id ) ) . '-template.json',
			) );
		} else {
			wp_send_json_error( __( 'Failed to export template.', 'addify_rfq' ) );
		}
	}

	/**
	 * Export template data as JSON.
	 */
	public function export_template( $template_id ) {
		$template = get_post( $template_id );
		if ( ! $template || $template->post_type !== self::POST_TYPE ) {
			return false;
		}

		$export_data = array(
			'template_info' => array(
				'title' => $template->post_title,
				'description' => get_post_meta( $template_id, '_afrfq_template_description', true ),
				'category' => get_post_meta( $template_id, '_afrfq_template_category', true ),
				'industry' => get_post_meta( $template_id, '_afrfq_template_industry', true ),
				'tags' => get_post_meta( $template_id, '_afrfq_template_tags', true ),
				'version' => get_post_meta( $template_id, '_afrfq_template_version', true ),
				'author' => get_post_meta( $template_id, '_afrfq_template_author', true ),
				'export_date' => current_time( 'mysql' ),
				'plugin_version' => AFRFQ_PLUGIN_VERSION,
			),
			'fields' => get_post_meta( $template_id, '_afrfq_template_fields', true ),
		);

		return $export_data;
	}

	/**
	 * AJAX handler for importing a template.
	 */
	public function ajax_import_template() {
		check_ajax_referer( 'afrfq_templates_nonce', 'nonce' );

		if ( ! current_user_can( 'manage_options' ) ) {
			wp_die( __( 'You do not have sufficient permissions to access this page.', 'addify_rfq' ) );
		}

		if ( ! isset( $_FILES['template_file'] ) ) {
			wp_send_json_error( __( 'No file uploaded.', 'addify_rfq' ) );
		}

		$file = $_FILES['template_file'];
		$file_content = file_get_contents( $file['tmp_name'] );
		$template_data = json_decode( $file_content, true );

		if ( ! $template_data || ! isset( $template_data['template_info'] ) ) {
			wp_send_json_error( __( 'Invalid template file.', 'addify_rfq' ) );
		}

		$template_id = $this->import_template( $template_data );
		if ( $template_id ) {
			wp_send_json_success( array(
				'message' => __( 'Template imported successfully!', 'addify_rfq' ),
				'template_id' => $template_id,
			) );
		} else {
			wp_send_json_error( __( 'Failed to import template.', 'addify_rfq' ) );
		}
	}

	/**
	 * Import template from data array.
	 */
	public function import_template( $template_data ) {
		$template_info = $template_data['template_info'];

		$template_id = wp_insert_post( array(
			'post_type' => self::POST_TYPE,
			'post_title' => $template_info['title'] . ' (Imported)',
			'post_content' => $template_info['description'] ?? '',
			'post_status' => 'publish',
		) );

		if ( is_wp_error( $template_id ) ) {
			return false;
		}

		// Save template meta data
		update_post_meta( $template_id, '_afrfq_template_description', $template_info['description'] ?? '' );
		update_post_meta( $template_id, '_afrfq_template_category', $template_info['category'] ?? '' );
		update_post_meta( $template_id, '_afrfq_template_industry', $template_info['industry'] ?? '' );
		update_post_meta( $template_id, '_afrfq_template_tags', $template_info['tags'] ?? '' );
		update_post_meta( $template_id, '_afrfq_template_version', $template_info['version'] ?? '1.0.0' );
		update_post_meta( $template_id, '_afrfq_template_author', $template_info['author'] ?? '' );
		update_post_meta( $template_id, '_afrfq_template_fields', $template_data['fields'] ?? array() );

		return $template_id;
	}

	/**
	 * AJAX handler for template preview.
	 */
	public function ajax_preview_template() {
		check_ajax_referer( 'afrfq_templates_nonce', 'nonce' );

		$template_id = intval( $_POST['template_id'] );
		if ( ! $template_id ) {
			wp_send_json_error( __( 'Invalid template ID.', 'addify_rfq' ) );
		}

		$template_fields = get_post_meta( $template_id, '_afrfq_template_fields', true );
		if ( ! is_array( $template_fields ) ) {
			wp_send_json_error( __( 'Template has no fields configured.', 'addify_rfq' ) );
		}

		$preview_html = $this->generate_template_preview( $template_fields );
		wp_send_json_success( array( 'preview_html' => $preview_html ) );
	}

	/**
	 * Generate HTML preview of template fields.
	 */
	private function generate_template_preview( $fields ) {
		ob_start();
		?>
		<div class="afrfq-template-preview">
			<?php foreach ( $fields as $field ) : ?>
				<div class="afrfq-preview-field afrfq-field-<?php echo esc_attr( $field['type'] ?? 'text' ); ?>">
					<label class="afrfq-preview-label">
						<?php echo esc_html( $field['label'] ?? 'Field Label' ); ?>
						<?php if ( ( $field['required'] ?? 'no' ) === 'yes' ) : ?>
							<span class="required">*</span>
						<?php endif; ?>
					</label>
					<?php
					switch ( $field['type'] ?? 'text' ) {
						case 'textarea':
							echo '<textarea class="afrfq-preview-input" placeholder="' . esc_attr( $field['placeholder'] ?? '' ) . '" disabled></textarea>';
							break;
						case 'select':
							echo '<select class="afrfq-preview-input" disabled>';
							echo '<option>' . esc_html( $field['placeholder'] ?? 'Select an option' ) . '</option>';
							if ( ! empty( $field['options'] ) ) {
								foreach ( $field['options'] as $option ) {
									echo '<option>' . esc_html( $option ) . '</option>';
								}
							}
							echo '</select>';
							break;
						case 'radio':
						case 'checkbox':
							if ( ! empty( $field['options'] ) ) {
								foreach ( $field['options'] as $option ) {
									echo '<label class="afrfq-preview-option">';
									echo '<input type="' . esc_attr( $field['type'] ) . '" disabled> ';
									echo esc_html( $option );
									echo '</label>';
								}
							}
							break;
						case 'file':
							echo '<input type="file" class="afrfq-preview-input" disabled>';
							break;
						default:
							echo '<input type="' . esc_attr( $field['type'] ?? 'text' ) . '" class="afrfq-preview-input" placeholder="' . esc_attr( $field['placeholder'] ?? '' ) . '" disabled>';
							break;
					}
					?>
					<?php if ( ! empty( $field['description'] ) ) : ?>
						<small class="afrfq-preview-description"><?php echo esc_html( $field['description'] ); ?></small>
					<?php endif; ?>
				</div>
			<?php endforeach; ?>
		</div>
		<?php
		return ob_get_clean();
	}

	/**
	 * Create default industry templates.
	 */
	public function create_default_templates() {
		// Only create if no templates exist
		$existing_templates = get_posts( array(
			'post_type' => self::POST_TYPE,
			'posts_per_page' => 1,
			'post_status' => 'any',
		) );

		if ( ! empty( $existing_templates ) ) {
			return;
		}

		$this->create_basic_contact_template();
		$this->create_construction_template();
		$this->create_healthcare_template();
		$this->create_manufacturing_template();
		$this->create_services_template();
	}

	/**
	 * Create basic contact template.
	 */
	private function create_basic_contact_template() {
		$template_id = wp_insert_post( array(
			'post_type' => self::POST_TYPE,
			'post_title' => __( 'Basic Contact Form', 'addify_rfq' ),
			'post_content' => __( 'A simple contact form with essential fields for general quote requests.', 'addify_rfq' ),
			'post_status' => 'publish',
		) );

		if ( ! is_wp_error( $template_id ) ) {
			update_post_meta( $template_id, '_afrfq_template_category', 'general' );
			update_post_meta( $template_id, '_afrfq_template_industry', 'general' );
			update_post_meta( $template_id, '_afrfq_template_description', __( 'Basic contact form suitable for any business type', 'addify_rfq' ) );
			update_post_meta( $template_id, '_afrfq_template_tags', 'basic, contact, general' );
			update_post_meta( $template_id, '_afrfq_template_version', '1.0.0' );
			update_post_meta( $template_id, '_afrfq_template_author', 'Addify' );

			$fields = array(
				array(
					'name' => 'customer_name',
					'type' => 'text',
					'label' => __( 'Full Name', 'addify_rfq' ),
					'placeholder' => __( 'Enter your full name', 'addify_rfq' ),
					'required' => 'yes',
					'width' => 'half_width',
					'order' => 1,
				),
				array(
					'name' => 'customer_email',
					'type' => 'email',
					'label' => __( 'Email Address', 'addify_rfq' ),
					'placeholder' => __( 'Enter your email address', 'addify_rfq' ),
					'required' => 'yes',
					'width' => 'half_width',
					'order' => 2,
				),
				array(
					'name' => 'customer_phone',
					'type' => 'phone',
					'label' => __( 'Phone Number', 'addify_rfq' ),
					'placeholder' => __( 'Enter your phone number', 'addify_rfq' ),
					'required' => 'no',
					'width' => 'half_width',
					'order' => 3,
				),
				array(
					'name' => 'customer_company',
					'type' => 'text',
					'label' => __( 'Company Name', 'addify_rfq' ),
					'placeholder' => __( 'Enter your company name', 'addify_rfq' ),
					'required' => 'no',
					'width' => 'half_width',
					'order' => 4,
				),
				array(
					'name' => 'quote_message',
					'type' => 'textarea',
					'label' => __( 'Additional Information', 'addify_rfq' ),
					'placeholder' => __( 'Please provide any additional details about your quote request', 'addify_rfq' ),
					'required' => 'no',
					'width' => 'full_width',
					'order' => 5,
				),
			);

			update_post_meta( $template_id, '_afrfq_template_fields', $fields );
		}
	}

	/**
	 * Create construction industry template.
	 */
	private function create_construction_template() {
		$template_id = wp_insert_post( array(
			'post_type' => self::POST_TYPE,
			'post_title' => __( 'Construction Quote Form', 'addify_rfq' ),
			'post_content' => __( 'Specialized form for construction and building projects with project-specific fields.', 'addify_rfq' ),
			'post_status' => 'publish',
		) );

		if ( ! is_wp_error( $template_id ) ) {
			update_post_meta( $template_id, '_afrfq_template_category', 'industry' );
			update_post_meta( $template_id, '_afrfq_template_industry', 'construction' );
			update_post_meta( $template_id, '_afrfq_template_description', __( 'Form designed for construction companies and contractors', 'addify_rfq' ) );
			update_post_meta( $template_id, '_afrfq_template_tags', 'construction, building, contractor' );
			update_post_meta( $template_id, '_afrfq_template_version', '1.0.0' );
			update_post_meta( $template_id, '_afrfq_template_author', 'Addify' );

			$fields = array(
				array(
					'name' => 'client_name',
					'type' => 'text',
					'label' => __( 'Client Name', 'addify_rfq' ),
					'required' => 'yes',
					'width' => 'half_width',
					'order' => 1,
				),
				array(
					'name' => 'client_email',
					'type' => 'email',
					'label' => __( 'Email Address', 'addify_rfq' ),
					'required' => 'yes',
					'width' => 'half_width',
					'order' => 2,
				),
				array(
					'name' => 'project_type',
					'type' => 'select',
					'label' => __( 'Project Type', 'addify_rfq' ),
					'required' => 'yes',
					'width' => 'half_width',
					'options' => array( 'Residential Construction', 'Commercial Construction', 'Renovation', 'Repair Work', 'Other' ),
					'order' => 3,
				),
				array(
					'name' => 'project_location',
					'type' => 'text',
					'label' => __( 'Project Location', 'addify_rfq' ),
					'required' => 'yes',
					'width' => 'half_width',
					'order' => 4,
				),
				array(
					'name' => 'project_timeline',
					'type' => 'select',
					'label' => __( 'Desired Timeline', 'addify_rfq' ),
					'required' => 'no',
					'width' => 'half_width',
					'options' => array( 'ASAP', '1-3 months', '3-6 months', '6+ months', 'Flexible' ),
					'order' => 5,
				),
				array(
					'name' => 'budget_range',
					'type' => 'select',
					'label' => __( 'Budget Range', 'addify_rfq' ),
					'required' => 'no',
					'width' => 'half_width',
					'options' => array( 'Under $10,000', '$10,000 - $50,000', '$50,000 - $100,000', '$100,000+', 'To be discussed' ),
					'order' => 6,
				),
				array(
					'name' => 'project_description',
					'type' => 'textarea',
					'label' => __( 'Project Description', 'addify_rfq' ),
					'placeholder' => __( 'Please describe your construction project in detail', 'addify_rfq' ),
					'required' => 'yes',
					'width' => 'full_width',
					'order' => 7,
				),
				array(
					'name' => 'project_plans',
					'type' => 'file',
					'label' => __( 'Project Plans/Documents', 'addify_rfq' ),
					'description' => __( 'Upload any relevant plans, drawings, or documents', 'addify_rfq' ),
					'required' => 'no',
					'width' => 'full_width',
					'file_types' => 'pdf,doc,docx,jpg,png,dwg',
					'order' => 8,
				),
			);

			update_post_meta( $template_id, '_afrfq_template_fields', $fields );
		}
	}

	/**
	 * Create healthcare industry template.
	 */
	private function create_healthcare_template() {
		$template_id = wp_insert_post( array(
			'post_type' => self::POST_TYPE,
			'post_title' => __( 'Healthcare Services Quote Form', 'addify_rfq' ),
			'post_content' => __( 'Form designed for healthcare service providers and medical equipment suppliers.', 'addify_rfq' ),
			'post_status' => 'publish',
		) );

		if ( ! is_wp_error( $template_id ) ) {
			update_post_meta( $template_id, '_afrfq_template_category', 'industry' );
			update_post_meta( $template_id, '_afrfq_template_industry', 'healthcare' );
			update_post_meta( $template_id, '_afrfq_template_description', __( 'Specialized form for healthcare and medical services', 'addify_rfq' ) );
			update_post_meta( $template_id, '_afrfq_template_tags', 'healthcare, medical, equipment' );
			update_post_meta( $template_id, '_afrfq_template_version', '1.0.0' );
			update_post_meta( $template_id, '_afrfq_template_author', 'Addify' );

			$fields = array(
				array(
					'name' => 'facility_name',
					'type' => 'text',
					'label' => __( 'Healthcare Facility Name', 'addify_rfq' ),
					'required' => 'yes',
					'width' => 'half_width',
					'order' => 1,
				),
				array(
					'name' => 'contact_person',
					'type' => 'text',
					'label' => __( 'Contact Person', 'addify_rfq' ),
					'required' => 'yes',
					'width' => 'half_width',
					'order' => 2,
				),
				array(
					'name' => 'contact_email',
					'type' => 'email',
					'label' => __( 'Email Address', 'addify_rfq' ),
					'required' => 'yes',
					'width' => 'half_width',
					'order' => 3,
				),
				array(
					'name' => 'facility_type',
					'type' => 'select',
					'label' => __( 'Facility Type', 'addify_rfq' ),
					'required' => 'yes',
					'width' => 'half_width',
					'options' => array( 'Hospital', 'Clinic', 'Dental Office', 'Laboratory', 'Pharmacy', 'Other' ),
					'order' => 4,
				),
				array(
					'name' => 'service_category',
					'type' => 'checkbox',
					'label' => __( 'Service Categories', 'addify_rfq' ),
					'required' => 'yes',
					'width' => 'full_width',
					'options' => array( 'Medical Equipment', 'Supplies', 'Maintenance Services', 'Training', 'Consultation' ),
					'order' => 5,
				),
				array(
					'name' => 'urgency_level',
					'type' => 'radio',
					'label' => __( 'Urgency Level', 'addify_rfq' ),
					'required' => 'yes',
					'width' => 'full_width',
					'options' => array( 'Emergency (24-48 hours)', 'Urgent (1 week)', 'Standard (2-4 weeks)', 'Non-urgent (1+ months)' ),
					'order' => 6,
				),
				array(
					'name' => 'requirements_description',
					'type' => 'textarea',
					'label' => __( 'Detailed Requirements', 'addify_rfq' ),
					'placeholder' => __( 'Please describe your specific healthcare service or equipment requirements', 'addify_rfq' ),
					'required' => 'yes',
					'width' => 'full_width',
					'order' => 7,
				),
			);

			update_post_meta( $template_id, '_afrfq_template_fields', $fields );
		}
	}

	/**
	 * Create manufacturing industry template.
	 */
	private function create_manufacturing_template() {
		$template_id = wp_insert_post( array(
			'post_type' => self::POST_TYPE,
			'post_title' => __( 'Manufacturing Quote Form', 'addify_rfq' ),
			'post_content' => __( 'Form designed for manufacturing companies and industrial suppliers.', 'addify_rfq' ),
			'post_status' => 'publish',
		) );

		if ( ! is_wp_error( $template_id ) ) {
			update_post_meta( $template_id, '_afrfq_template_category', 'industry' );
			update_post_meta( $template_id, '_afrfq_template_industry', 'manufacturing' );
			update_post_meta( $template_id, '_afrfq_template_description', __( 'Specialized form for manufacturing and industrial services', 'addify_rfq' ) );
			update_post_meta( $template_id, '_afrfq_template_tags', 'manufacturing, industrial, production' );
			update_post_meta( $template_id, '_afrfq_template_version', '1.0.0' );
			update_post_meta( $template_id, '_afrfq_template_author', 'Addify' );

			$fields = array(
				array(
					'name' => 'company_name',
					'type' => 'text',
					'label' => __( 'Company Name', 'addify_rfq' ),
					'required' => 'yes',
					'width' => 'half_width',
					'order' => 1,
				),
				array(
					'name' => 'procurement_contact',
					'type' => 'text',
					'label' => __( 'Procurement Contact', 'addify_rfq' ),
					'required' => 'yes',
					'width' => 'half_width',
					'order' => 2,
				),
				array(
					'name' => 'quantity_required',
					'type' => 'number',
					'label' => __( 'Quantity Required', 'addify_rfq' ),
					'required' => 'yes',
					'width' => 'third_width',
					'min_value' => '1',
					'order' => 3,
				),
				array(
					'name' => 'delivery_timeline',
					'type' => 'select',
					'label' => __( 'Delivery Timeline', 'addify_rfq' ),
					'required' => 'yes',
					'width' => 'third_width',
					'options' => array( '1-2 weeks', '3-4 weeks', '1-2 months', '3+ months', 'Flexible' ),
					'order' => 4,
				),
				array(
					'name' => 'quality_standards',
					'type' => 'checkbox',
					'label' => __( 'Required Quality Standards', 'addify_rfq' ),
					'required' => 'no',
					'width' => 'third_width',
					'options' => array( 'ISO 9001', 'ISO 14001', 'AS9100', 'TS 16949', 'Other' ),
					'order' => 5,
				),
				array(
					'name' => 'product_specifications',
					'type' => 'textarea',
					'label' => __( 'Product Specifications', 'addify_rfq' ),
					'placeholder' => __( 'Please provide detailed product specifications, materials, dimensions, etc.', 'addify_rfq' ),
					'required' => 'yes',
					'width' => 'full_width',
					'order' => 6,
				),
				array(
					'name' => 'technical_drawings',
					'type' => 'file',
					'label' => __( 'Technical Drawings/Specifications', 'addify_rfq' ),
					'description' => __( 'Upload CAD files, technical drawings, or specification documents', 'addify_rfq' ),
					'required' => 'no',
					'width' => 'full_width',
					'file_types' => 'pdf,dwg,step,iges,doc,docx',
					'order' => 7,
				),
			);

			update_post_meta( $template_id, '_afrfq_template_fields', $fields );
		}
	}

	/**
	 * Create services industry template.
	 */
	private function create_services_template() {
		$template_id = wp_insert_post( array(
			'post_type' => self::POST_TYPE,
			'post_title' => __( 'Professional Services Quote Form', 'addify_rfq' ),
			'post_content' => __( 'Form designed for professional service providers and consultants.', 'addify_rfq' ),
			'post_status' => 'publish',
		) );

		if ( ! is_wp_error( $template_id ) ) {
			update_post_meta( $template_id, '_afrfq_template_category', 'industry' );
			update_post_meta( $template_id, '_afrfq_template_industry', 'services' );
			update_post_meta( $template_id, '_afrfq_template_description', __( 'Form for professional services and consulting', 'addify_rfq' ) );
			update_post_meta( $template_id, '_afrfq_template_tags', 'services, consulting, professional' );
			update_post_meta( $template_id, '_afrfq_template_version', '1.0.0' );
			update_post_meta( $template_id, '_afrfq_template_author', 'Addify' );

			$fields = array(
				array(
					'name' => 'client_organization',
					'type' => 'text',
					'label' => __( 'Organization Name', 'addify_rfq' ),
					'required' => 'yes',
					'width' => 'half_width',
					'order' => 1,
				),
				array(
					'name' => 'decision_maker',
					'type' => 'text',
					'label' => __( 'Decision Maker', 'addify_rfq' ),
					'required' => 'yes',
					'width' => 'half_width',
					'order' => 2,
				),
				array(
					'name' => 'service_type',
					'type' => 'select',
					'label' => __( 'Service Type', 'addify_rfq' ),
					'required' => 'yes',
					'width' => 'half_width',
					'options' => array( 'Consulting', 'Training', 'Implementation', 'Support', 'Audit', 'Other' ),
					'order' => 3,
				),
				array(
					'name' => 'project_duration',
					'type' => 'select',
					'label' => __( 'Expected Project Duration', 'addify_rfq' ),
					'required' => 'no',
					'width' => 'half_width',
					'options' => array( '1-2 weeks', '1 month', '2-3 months', '6+ months', 'Ongoing', 'To be determined' ),
					'order' => 4,
				),
				array(
					'name' => 'service_objectives',
					'type' => 'textarea',
					'label' => __( 'Service Objectives', 'addify_rfq' ),
					'placeholder' => __( 'Please describe what you hope to achieve with this service', 'addify_rfq' ),
					'required' => 'yes',
					'width' => 'full_width',
					'order' => 5,
				),
				array(
					'name' => 'current_challenges',
					'type' => 'textarea',
					'label' => __( 'Current Challenges', 'addify_rfq' ),
					'placeholder' => __( 'Describe any current challenges or pain points you are facing', 'addify_rfq' ),
					'required' => 'no',
					'width' => 'full_width',
					'order' => 6,
				),
				array(
					'name' => 'preferred_communication',
					'type' => 'radio',
					'label' => __( 'Preferred Communication Method', 'addify_rfq' ),
					'required' => 'no',
					'width' => 'full_width',
					'options' => array( 'Email', 'Phone', 'Video Call', 'In-person Meeting' ),
					'order' => 7,
				),
			);

			update_post_meta( $template_id, '_afrfq_template_fields', $fields );
		}
	}

	/**
	 * Get template categories.
	 */
	public function get_template_categories() {
		return array(
			'general' => __( 'General', 'addify_rfq' ),
			'industry' => __( 'Industry Specific', 'addify_rfq' ),
			'custom' => __( 'Custom', 'addify_rfq' ),
		);
	}

	/**
	 * Get template industries.
	 */
	public function get_template_industries() {
		return array(
			'general' => __( 'General', 'addify_rfq' ),
			'construction' => __( 'Construction', 'addify_rfq' ),
			'healthcare' => __( 'Healthcare', 'addify_rfq' ),
			'manufacturing' => __( 'Manufacturing', 'addify_rfq' ),
			'services' => __( 'Professional Services', 'addify_rfq' ),
			'technology' => __( 'Technology', 'addify_rfq' ),
			'retail' => __( 'Retail', 'addify_rfq' ),
			'education' => __( 'Education', 'addify_rfq' ),
			'finance' => __( 'Finance', 'addify_rfq' ),
			'other' => __( 'Other', 'addify_rfq' ),
		);
	}

	/**
	 * AJAX handler for duplicating a template.
	 */
	public function ajax_duplicate_template() {
		check_ajax_referer( 'afrfq_templates_nonce', 'nonce' );

		if ( ! current_user_can( 'manage_options' ) ) {
			wp_die( __( 'You do not have sufficient permissions to access this page.', 'addify_rfq' ) );
		}

		$template_id = intval( $_POST['template_id'] );
		if ( ! $template_id ) {
			wp_send_json_error( __( 'Invalid template ID.', 'addify_rfq' ) );
		}

		$duplicated_id = $this->duplicate_template( $template_id );
		if ( $duplicated_id ) {
			wp_send_json_success( array(
				'message' => __( 'Template duplicated successfully!', 'addify_rfq' ),
				'template_id' => $duplicated_id,
				'edit_url' => admin_url( 'post.php?post=' . $duplicated_id . '&action=edit' ),
			) );
		} else {
			wp_send_json_error( __( 'Failed to duplicate template.', 'addify_rfq' ) );
		}
	}

	/**
	 * Duplicate a template.
	 */
	public function duplicate_template( $template_id ) {
		$original_template = get_post( $template_id );
		if ( ! $original_template || $original_template->post_type !== self::POST_TYPE ) {
			return false;
		}

		// Create new template
		$new_template_id = wp_insert_post( array(
			'post_type' => self::POST_TYPE,
			'post_title' => $original_template->post_title . ' (Copy)',
			'post_content' => $original_template->post_content,
			'post_status' => 'draft', // Set as draft initially
		) );

		if ( is_wp_error( $new_template_id ) ) {
			return false;
		}

		// Copy all meta data
		$meta_fields = array(
			'_afrfq_template_category',
			'_afrfq_template_industry',
			'_afrfq_template_description',
			'_afrfq_template_tags',
			'_afrfq_template_version',
			'_afrfq_template_author',
			'_afrfq_template_fields',
		);

		foreach ( $meta_fields as $meta_key ) {
			$meta_value = get_post_meta( $template_id, $meta_key, true );
			if ( $meta_value ) {
				update_post_meta( $new_template_id, $meta_key, $meta_value );
			}
		}

		// Update version for the copy
		$version = get_post_meta( $template_id, '_afrfq_template_version', true );
		if ( $version ) {
			$version_parts = explode( '.', $version );
			if ( count( $version_parts ) >= 3 ) {
				$version_parts[2] = intval( $version_parts[2] ) + 1;
				$new_version = implode( '.', $version_parts );
				update_post_meta( $new_template_id, '_afrfq_template_version', $new_version );
			}
		}

		return $new_template_id;
	}
}
