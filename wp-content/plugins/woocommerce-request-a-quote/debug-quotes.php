<?php
/**
 * Debug Quotes - Check if quotes are being created and saved
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Only run for administrators
if ( ! current_user_can( 'manage_options' ) ) {
    wp_die( 'Access denied' );
}

echo '<div style="padding: 20px; font-family: Arial, sans-serif;">';
echo '<h1>Quote System Debug</h1>';

// Test 1: Check if post type exists
echo '<h2>Test 1: Post Type Registration</h2>';
if ( post_type_exists( 'addify_quote' ) ) {
    echo '<p style="color: green;">✓ addify_quote post type is registered</p>';
} else {
    echo '<p style="color: red;">✗ addify_quote post type is NOT registered</p>';
}

// Test 2: Check existing quotes in database
echo '<h2>Test 2: Existing Quotes in Database</h2>';
$quotes = get_posts( array(
    'post_type' => 'addify_quote',
    'post_status' => array( 'publish', 'draft', 'private' ),
    'numberposts' => -1,
    'meta_query' => array()
) );

echo '<p>Found ' . count( $quotes ) . ' quotes in database</p>';

if ( ! empty( $quotes ) ) {
    echo '<table border="1" cellpadding="5" style="border-collapse: collapse; width: 100%;">';
    echo '<tr><th>ID</th><th>Title</th><th>Status</th><th>Date</th><th>Customer</th><th>Quote Status</th></tr>';
    
    foreach ( $quotes as $quote ) {
        $quote_status = get_post_meta( $quote->ID, 'quote_status', true );
        $customer_name = get_post_meta( $quote->ID, 'afrfq_customer_name', true );
        if ( empty( $customer_name ) ) {
            $customer_name = get_post_meta( $quote->ID, 'customer_name', true );
        }
        
        echo '<tr>';
        echo '<td>' . $quote->ID . '</td>';
        echo '<td>' . esc_html( $quote->post_title ) . '</td>';
        echo '<td>' . $quote->post_status . '</td>';
        echo '<td>' . $quote->post_date . '</td>';
        echo '<td>' . esc_html( $customer_name ) . '</td>';
        echo '<td>' . esc_html( $quote_status ) . '</td>';
        echo '</tr>';
    }
    echo '</table>';
} else {
    echo '<p style="color: orange;">No quotes found in database</p>';
}

// Test 3: Check WooCommerce session
echo '<h2>Test 3: WooCommerce Session</h2>';
if ( class_exists( 'WC' ) && WC()->session ) {
    echo '<p style="color: green;">✓ WooCommerce session is available</p>';
    
    $session_quotes = WC()->session->get( 'quotes' );
    if ( ! empty( $session_quotes ) ) {
        echo '<p>Current session has ' . count( $session_quotes ) . ' quote items</p>';
        echo '<pre>' . print_r( $session_quotes, true ) . '</pre>';
    } else {
        echo '<p>No quote items in current session</p>';
    }
} else {
    echo '<p style="color: red;">✗ WooCommerce session is NOT available</p>';
}

// Test 4: Check quote fields
echo '<h2>Test 4: Quote Fields</h2>';
$quote_fields = get_posts( array(
    'post_type' => 'addify_rfq_fields',
    'post_status' => 'publish',
    'numberposts' => -1
) );

echo '<p>Found ' . count( $quote_fields ) . ' quote fields</p>';

if ( ! empty( $quote_fields ) ) {
    echo '<ul>';
    foreach ( $quote_fields as $field ) {
        $field_name = get_post_meta( $field->ID, 'afrfq_field_name', true );
        $field_type = get_post_meta( $field->ID, 'afrfq_field_type', true );
        $field_enabled = get_post_meta( $field->ID, 'afrfq_field_enable', true );
        
        echo '<li>' . esc_html( $field->post_title ) . ' (' . $field_name . ') - Type: ' . $field_type . ' - Enabled: ' . $field_enabled . '</li>';
    }
    echo '</ul>';
}

// Test 5: Check plugin settings
echo '<h2>Test 5: Plugin Settings</h2>';
$important_settings = array(
    'afrfq_success_message' => 'Success Message',
    'afrfq_redirect_after_submission' => 'Redirect After Submission',
    'afrfq_redirect_url' => 'Redirect URL',
    'addify_atq_page_id' => 'Quote Page ID'
);

echo '<ul>';
foreach ( $important_settings as $setting => $label ) {
    $value = get_option( $setting );
    echo '<li><strong>' . $label . ':</strong> ' . ( empty( $value ) ? 'Not set' : esc_html( $value ) ) . '</li>';
}
echo '</ul>';

// Test 6: Test quote creation manually
echo '<h2>Test 6: Manual Quote Creation Test</h2>';

if ( isset( $_POST['test_quote_creation'] ) ) {
    echo '<div style="background: #f0f0f0; padding: 10px; margin: 10px 0; border-left: 4px solid #0073aa;">';
    echo '<h3>Testing Quote Creation...</h3>';
    
    // Initialize WooCommerce session if needed
    if ( ! WC()->session ) {
        WC()->session = new WC_Session_Handler();
        WC()->session->init();
    }
    
    // Create test data
    $test_data = array(
        'afrfq_customer_name' => 'Test Customer',
        'afrfq_company_name' => 'Test Company',
        'afrfq_email_address' => '<EMAIL>',
        'afrfq_phone_number' => '************',
        'afrfq_state' => 'Test State'
    );
    
    try {
        // Create quote instance
        $quote_obj = new AF_R_F_Q_Quote();
        
        // Test the insertion
        ob_start();
        $quote_obj->insert_new_quote( $test_data );
        $output = ob_get_clean();
        
        if ( strpos( $output, 'hiddenField' ) !== false ) {
            // Extract quote ID from output
            preg_match( '/value="(\d+)"/', $output, $matches );
            $quote_id = isset( $matches[1] ) ? $matches[1] : 'unknown';
            echo "<p style='color: green;'>✓ Quote created successfully! Quote ID: {$quote_id}</p>";
            
            // Verify the quote exists in database
            $created_quote = get_post( $quote_id );
            if ( $created_quote ) {
                echo "<p style='color: green;'>✓ Quote verified in database</p>";
                echo "<p>Title: " . esc_html( $created_quote->post_title ) . "</p>";
                echo "<p>Status: " . $created_quote->post_status . "</p>";
                
                // Check meta data
                $customer_name = get_post_meta( $quote_id, 'afrfq_customer_name', true );
                $quote_status = get_post_meta( $quote_id, 'quote_status', true );
                echo "<p>Customer Name: " . esc_html( $customer_name ) . "</p>";
                echo "<p>Quote Status: " . esc_html( $quote_status ) . "</p>";
            } else {
                echo "<p style='color: red;'>✗ Quote not found in database after creation</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ Quote creation failed - no quote ID returned</p>";
            echo "<p>Output: " . esc_html( $output ) . "</p>";
        }
        
    } catch ( Exception $e ) {
        echo "<p style='color: red;'>✗ Exception during quote creation: " . esc_html( $e->getMessage() ) . "</p>";
    }
    
    echo '</div>';
}

echo '<form method="post">';
echo '<input type="hidden" name="test_quote_creation" value="1">';
echo '<button type="submit" style="background: #0073aa; color: white; padding: 10px 20px; border: none; border-radius: 3px; cursor: pointer;">Test Quote Creation</button>';
echo '</form>';

// Test 7: Check database tables
echo '<h2>Test 7: Database Tables</h2>';
global $wpdb;

$posts_table = $wpdb->posts;
$postmeta_table = $wpdb->postmeta;

// Check if tables exist
$tables_exist = true;
$tables_to_check = array( $posts_table, $postmeta_table );

foreach ( $tables_to_check as $table ) {
    $table_exists = $wpdb->get_var( "SHOW TABLES LIKE '$table'" );
    if ( $table_exists ) {
        echo "<p style='color: green;'>✓ Table {$table} exists</p>";
    } else {
        echo "<p style='color: red;'>✗ Table {$table} does NOT exist</p>";
        $tables_exist = false;
    }
}

if ( $tables_exist ) {
    // Check for quote posts directly in database
    $quote_count = $wpdb->get_var( "SELECT COUNT(*) FROM {$posts_table} WHERE post_type = 'addify_quote'" );
    echo "<p>Direct database query found {$quote_count} quote posts</p>";
}

echo '</div>';
?>

<style>
table {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
}

th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

tr:nth-child(even) {
    background-color: #f9f9f9;
}

pre {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 3px;
    overflow-x: auto;
    max-height: 300px;
}
</style>
